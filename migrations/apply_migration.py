#!/usr/bin/env python3

import sys
import logging
from pathlib import Path
from importlib import import_module

from facades.elasticpricedb.db import ElasticPriceDbFacade

# Add project root to path to import from project modules
sys.path.append(str(Path(__file__).parent.parent))

logger = logging.getLogger(__name__)

db_migration = ElasticPriceDbFacade.instance().db_query()

def init_migration() -> bool:
    """Initialize the migration table."""
    logger.info("Initializing migration table")
    # Check if migration table exists
    if db_migration.query_one("SELECT * FROM information_schema.tables WHERE table_schema = 'elastic_price' AND table_name = 'migration'"):
        logger.info("Migration table already exists")
        return True
    
    # Create migration table    
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.migration",
        """
        CREATE TABLE elastic_price.migration (
            version VARCHAR(255),
            applied TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,
        "ALTER TABLE elastic_price.migration ADD PRIMARY KEY (version)",
    ]

    for statement in sql_statements:
        db_migration.execute(statement)
    
    logger.info("Migration table created")
    return True
#def

def is_migration_applied(migration_name: str, action: str = "up") -> bool:
    m =  db_migration.query_one(
        "SELECT * FROM elastic_price.migration WHERE version = %s", (migration_name,)
    )

    if action == "up":
        if m:
            return True
        return False
    #if

    if action == "down":
        if m:
            return False
        return True
    #if

    return True
#def

def import_migration(migration_path: Path):
    module_path =  str(migration_path).replace("migrations/", "").replace("/", ".").replace(".py", "")

    return import_module(f"migrations.{module_path}")
#def


def apply_migration(migration_file, action="up", dry_run=False) -> bool:
    """Apply a SQL migration file to the database."""
    # Clean up the migration file path

    logger.info(f"Applying migration {migration_file}")
    migration_file = migration_file.replace(".py", "")
    migration_path = Path(migration_file + ".py")
    
    migration_name = migration_path.stem
    
    if is_migration_applied(migration_name, action):
        logger.info(f"Migration already applied: {migration_name}")
        return True
    
    if not migration_path.exists() or not migration_path.is_file():
        logger.error(f"Migration file not found: {migration_path}")
        return False
    

    try:
        # Import the module        
        mod = import_migration(migration_path)
        
        action = action.lower()
        
        match action:
            case "up":
                if not hasattr(mod, "up"):
                    logger.error(f"Migration file does not have an 'up' function: {migration_file}")
                    return False
                migrate_action = mod.up
            case "down":
                if not hasattr(mod, "down"):
                    logger.error(f"Migration file does not have a 'down' function: {migration_file}")
                    return False
                migrate_action = mod.down
            case _:
                logger.error(f"Invalid action: {action}")
                return False
        
        ok = migrate_action(dry_run=dry_run)

        if ok:
            if dry_run:
                logger.info(f"Migration applied (dry run): {migration_name}")
                return True
            
            if action == "up":
                db_migration.execute(
                    "INSERT INTO elastic_price.migration (version) VALUES (%s)", (migration_name,)
                )
            elif action == "down":
                db_migration.execute(
                    "DELETE FROM elastic_price.migration WHERE version = %s", (migration_name,)
                )
            logger.info(f"Migration applied: {migration_name}")
            return True
        else:
            logger.error(f"Migration failed: {migration_name}")
            return False    
    except ImportError as e:
        logger.error(f"Error applying migration {migration_file}: {e}", exc_info=True)
        return False
#def




def migrate_all(directory: str, action: str = "up", dry_run: bool = False, stop_on_error: bool = True, amount: int = -1) -> bool:
    """
    Run all migrations in a directory in alphabetical order.
    
    Args:
        directory: Directory containing migration files
        action: Migration action (up/down)
        dry_run: If True, print SQL without executing
        stop_on_error: If True, stop on first error
        amount: Amount of migrations to apply
        
    Returns:
        bool: True if all migrations succeeded, False otherwise
    """
    logger.info(f"Running all migrations in {directory} ({action})")
    
    directory = Path(directory)
    migration_files = sorted(list(directory.glob("*.py")), reverse=(action == "down"))

    if not migration_files:
        logger.warning(f"No migration files found in {directory}")
        return True

    # Track success/failure
    success_count = 0
    failure_count = 0
    apply_counter = 0
    
    # Run each migration in order
    for migration_path in migration_files:
        if amount > 0 and apply_counter >= amount:
            break

        # Skip __init__.py and other non-migration files
        if migration_path.name == "__init__.py":
            continue

        migration_file = str(migration_path)

        if is_migration_applied(migration_path.stem, action):
            continue

        apply_counter += 1
        
        try:
            result = apply_migration(migration_file, action, dry_run)
            
            if result:
                success_count += 1
            else:
                failure_count += 1
                if stop_on_error:
                    return False
        except Exception as e:
            logger.error(f"Error applying migration {migration_file}: {e}", exc_info=True)
            failure_count += 1
            if stop_on_error:
                logger.error("Stopping due to migration error")
                return False
    
    # Log summary
    logger.info(f"Migration summary: {success_count} succeeded, {failure_count} failed")
    
    # Return True only if all migrations succeeded
    return failure_count == 0