#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250703_sales_history --action=up --dry-run

def up(dry_run=False)->bool:

    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.sales_history",
        """
        CREATE TABLE elastic_price.sales_history (
            id bigserial PRIMARY KEY,
            asin VARCHAR(20),
            sku VARCHAR(50),
            marketplace_id VARCHAR(20),
            order_purchase_date DATE,
            customer_id BIGINT,
            total_orders INTEGER,
            total_quantity INTEGER,
            total_price INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE (asin, sku, marketplace_id, order_purchase_date, customer_id)
        );
        """,

        "CREATE INDEX sales_history_asin_idx ON elastic_price.sales_history (asin)",
        "CREATE INDEX sales_history_sku_idx ON elastic_price.sales_history (sku)",
        "CREATE INDEX sales_history_marketplace_id_idx ON elastic_price.sales_history (marketplace_id)",
        "CREATE INDEX sales_history_order_purchase_date_idx ON elastic_price.sales_history (order_purchase_date)",
        "CREATE INDEX sales_history_customer_id_idx ON elastic_price.sales_history (customer_id)",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.sales_history",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)
