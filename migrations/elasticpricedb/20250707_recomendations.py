#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250707_recomendations --action=up --dry-run

def up(dry_run=False)->bool:

    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.recommendation",
        """
        CREATE TABLE elastic_price.recommendation (
            id bigserial PRIMARY KEY,
            customer_id BIGINT,
            asin VARCHAR(20),
            sku VARCHAR(50),
            marketplace_id VARCHAR(20),
            recommended_action VARCHAR(20),
            probability_demand_increase NUMERIC(10, 6),
            training_loss NUMERIC(10, 6),
            recommendation_info JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE (customer_id, asin, sku, marketplace_id)
        );
        """,
        
        "CREATE INDEX recommendation_customer_id_idx ON elastic_price.recommendation (customer_id)",
        "CREATE INDEX recommendation_asin_idx ON elastic_price.recommendation (asin)",
        "CREATE INDEX recommendation_sku_idx ON elastic_price.recommendation (sku)",
        "CREATE INDEX recommendation_marketplace_id_idx ON elastic_price.recommendation (marketplace_id)",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.recommendation",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)
