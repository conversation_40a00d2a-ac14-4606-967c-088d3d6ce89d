#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250709_remove_tables --action=up --dry-run

def up(dry_run=False)->bool:

    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.data_train_history",
        "DROP TABLE IF EXISTS elastic_price.product_optimization_snapshot",
        "DROP TABLE IF EXISTS elastic_price.sales_history",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:
    return True
