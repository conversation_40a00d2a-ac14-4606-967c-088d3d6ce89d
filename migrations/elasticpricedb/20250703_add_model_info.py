#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250703_add_model_info --action=up --dry-run

def up(dry_run=False)->bool:

    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.trained_model_new",
        """
        CREATE TABLE elastic_price.trained_model_new (
            id serial8 PRIMARY KEY,
            model_name VARCHAR(100),
            active BOOLEAN DEFAULT TRUE,
            model_info JSONB NULL,
            model_dump BYTEA,
            loss NUMERIC(10, 6),
            accuracy NUMERIC(10, 6),
            training_data_size INTEGER,
            validation_data_size INTEGER,
            start_time TIMESTAMP WITH TIME ZONE,
            end_time TIMESTAMP WITH TIME ZONE, 
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,


        "INSERT INTO elastic_price.trained_model_new (model_name, active, model_info,  model_dump, loss, accuracy, training_data_size, validation_data_size, start_time, end_time, created_at) "
        "SELECT model_name, active, NULL, model_dump, loss, accuracy, training_data_size, validation_data_size, start_time, end_time, created_at FROM elastic_price.trained_model ORDER BY id ASC",

        "DROP TABLE IF EXISTS elastic_price.trained_model",
        "ALTER TABLE elastic_price.trained_model_new RENAME TO trained_model",

        "DROP INDEX IF EXISTS trained_model_model_name_idx",
        "DROP INDEX IF EXISTS trained_model_active_idx",

        "CREATE INDEX trained_model_model_name_idx ON elastic_price.trained_model (model_name)",
        "CREATE INDEX trained_model_active_idx ON elastic_price.trained_model (active)",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:

    return True
