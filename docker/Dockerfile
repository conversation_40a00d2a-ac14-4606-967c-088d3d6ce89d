# Build stage
FROM python:3.13.3-slim AS builder

WORKDIR /build

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Python build tools
RUN python3 -m pip install --no-cache-dir pip setuptools wheel

# Copy only dependency files
COPY setup.py .
COPY pyproject.toml .
COPY README.md .

# Install dependencies into /build
RUN pip3 wheel --no-cache-dir --no-deps --wheel-dir /build/wheels -e .

# Final stage
FROM python:3.13.3-slim

# Create work directory
WORKDIR /app

# Copy wheels from builder and install dependencies
COPY --from=builder /build/wheels /wheels
RUN pip3 install --no-cache-dir /wheels/* \
    && rm -rf /wheels

# Copy application code
COPY . .

# Set environment variables
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1
