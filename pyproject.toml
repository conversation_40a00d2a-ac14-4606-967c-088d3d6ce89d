[build-system]
requires = ["setuptools>=70.0.0", "wheel", "build"]
build-backend = "setuptools.build_meta"

[project]
name = "elastic_price"
dynamic = ["version", "description", "dependencies", "optional-dependencies"]
readme = "README.md"
license = "MIT"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
keywords = ["api", "cli", "python", "pricing", "optimization", "machine learning", "pytorch", "fastapi", "neural networks"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
    "Framework :: FastAPI",
    "Environment :: Console",
    "Environment :: Web Environment",
]
requires-python = ">=3.12.0"

[project.urls]
Homepage = "https://gitlab.sl.local/sellerlogic/elastic-price"
Repository = "https://gitlab.sl.local/sellerlogic/elastic-price"
Issues = "https://gitlab.sl.local/sellerlogic/elastic-price/-/issues"
Documentation = "https://gitlab.sl.local/sellerlogic/elastic-price/-/blob/master/README.md"

[project.scripts]
elastic-price = "cli.main:main"

[tool.black]
line-length = 88
target-version = ['py312', 'py313']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
    | \.venv
    | __pycache__
    | build
    | dist
    | \.eggs
    | logs
    | test_new_model
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["api", "cli", "core", "domains", "facades", "lib", "repositories", "utils"]
skip_glob = ["**/__pycache__", "**/logs", "**/test_new_model"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "test_new_model/",
    "logs/",
    "__pycache__/",
    ".venv/",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
    "logs",
    "test_new_model",
]