from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from lib.json import json_decode, json_encode


@dataclass
class RecommendationModel:
    id: Optional[int]
    customer_id: Optional[int]
    asin: Optional[str]
    sku: Optional[str]
    marketplace_id: Optional[str]
    recommended_action: Optional[str]
    probability_demand_increase: Optional[float]
    training_loss: Optional[float]
    recommendation_info: Optional[dict]
    created_at: Optional[datetime]
    modified_at: Optional[datetime]

    def to_dict(self) -> dict:
        """
        Convert the model instance to a dictionary.
        
        Returns:
            dict: Dictionary representation of the model
        """
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'asin': self.asin,
            'sku': self.sku,
            'marketplace_id': self.marketplace_id,
            'recommended_action': self.recommended_action,
            'probability_demand_increase': self.probability_demand_increase,
            'training_loss': self.training_loss,
            'recommendation_info': self.recommendation_info,
            'created_at': self.created_at,
            'modified_at': self.modified_at
        }

    def to_json(self) -> str:
        """
        Convert the model instance to a JSON string.
        
        Returns:
            str: JSON representation of the model
        """
        return json_encode(self.to_dict())

    @staticmethod
    def from_dict(d: dict) -> 'RecommendationModel':
        """
        Create a RecommendationModel instance from a dictionary.
        
        Args:
            d: Dictionary containing model data
            
        Returns:
            RecommendationModel: New instance created from the dictionary
        """

        recommendation_info = None
        if 'recommendation_info' in d and isinstance(d['recommendation_info'], dict):
            recommendation_info = d['recommendation_info']
        elif 'recommendation_info' in d and isinstance(d['recommendation_info'], str):
            recommendation_info = json_decode(d['recommendation_info'])

        return RecommendationModel(
            id=d.get('id', 0),
            customer_id=d.get('customer_id'),
            asin=d.get('asin'),
            sku=d.get('sku'),
            marketplace_id=d.get('marketplace_id'),
            recommended_action=d.get('recommended_action'),
            probability_demand_increase=d.get('probability_demand_increase'),
            training_loss=d.get('training_loss'),
            recommendation_info=recommendation_info,
            created_at=d.get('created_at'),
            modified_at=d.get('modified_at')
        )
        
    @staticmethod
    def from_json(json_str: str) -> list['RecommendationModel']:
        """
        Create RecommendationModel instance(s) from a JSON string.
        
        Args:
            json_str: JSON string containing model data
            
        Returns:
            list[RecommendationModel]: New instance(s) created from the JSON
            
        Raises:
            ValueError: If the JSON format is invalid
        """
        d = json_decode(json_str)

        if d is None:
            raise ValueError("Invalid JSON format")

        if isinstance(d, list):
            return RecommendationModel.from_dicts(d)
        elif isinstance(d, dict):
            return [RecommendationModel.from_dict(d)]
        
        raise ValueError("Invalid JSON format")

    @staticmethod
    def from_dicts(ds: list[dict]) -> list['RecommendationModel']:
        """
        Create multiple RecommendationModel instances from a list of dictionaries.
        
        Args:
            ds: List of dictionaries containing model data
            
        Returns:
            list[RecommendationModel]: List of new instances created from the dictionaries
        """
        return [RecommendationModel.from_dict(d) for d in ds]
