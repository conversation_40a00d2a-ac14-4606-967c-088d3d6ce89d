from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Union

from lib.json import json_decode, json_encode


@dataclass
class TrainedModel:
    """
    Represents a trained machine learning model stored in the database.
    
    This class maps to the elastic_price.trained_model table and provides
    methods for serialization and deserialization.
    """
    id: Optional[int]
    model_name: Optional[str]
    active: bool
    model_info: Optional[dict]
    model_dump: Optional[bytes]
    loss: Optional[float]
    accuracy: Optional[float]
    training_data_size: Optional[int]
    validation_data_size: Optional[int]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    created_at: Optional[datetime]

    def to_dict(self) -> dict:
        """
        Convert the model instance to a dictionary.
        
        Returns:
            dict: Dictionary representation of the model
        """
        return {
            'id': self.id,
            'model_name': self.model_name,
            'active': self.active,
            'model_info': self.model_info,
            'model_dump': self.model_dump,
            'loss': self.loss,
            'accuracy': self.accuracy,
            'training_data_size': self.training_data_size,
            'validation_data_size': self.validation_data_size,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'created_at': self.created_at
        }

    def to_json(self) -> str:
        """
        Convert the model instance to a JSON string.
        
        Returns:
            str: JSON representation of the model
        """
        return json_encode(self.to_dict())

    @staticmethod
    def from_dict(d: dict) -> 'TrainedModel':
        """
        Create a TrainedModel instance from a dictionary.
        
        Args:
            d: Dictionary containing model data
            
        Returns:
            TrainedModel: New instance created from the dictionary
        """

        model_info = None
        if 'model_info' in d and isinstance(d['model_info'], dict):
            model_info = d['model_info']
        elif 'model_info' in d and isinstance(d['model_info'], str):
            model_info = json_decode(d['model_info'])

        return TrainedModel(
            id=d.get('id', 0),
            model_name=d.get('model_name'),
            active=d.get('active', True),
            model_info=model_info,
            model_dump=d.get('model_dump'),
            loss=d.get('loss'),
            accuracy=d.get('accuracy'),
            training_data_size=d.get('training_data_size'),
            validation_data_size=d.get('validation_data_size'),
            start_time=d.get('start_time'),
            end_time=d.get('end_time'),
            created_at=d.get('created_at')
        )

    @staticmethod
    def from_json(json_str: str) -> list['TrainedModel']:
        """
        Create TrainedModel instance(s) from a JSON string.
        
        Args:
            json_str: JSON string containing model data
            
        Returns:
            list[TrainedModel]: New instance(s) created from the JSON
            
        Raises:
            ValueError: If the JSON format is invalid
        """
        d = json_decode(json_str)

        if d is None:
            raise ValueError("Invalid JSON format")

        if isinstance(d, list):
            return TrainedModel.from_dicts(d)
        elif isinstance(d, dict):
            return [TrainedModel.from_dict(d)]
        
        raise ValueError("Invalid JSON format")

    @staticmethod
    def from_dicts(ds: list[dict]) -> list['TrainedModel']:
        """
        Create multiple TrainedModel instances from a list of dictionaries.
        
        Args:
            ds: List of dictionaries containing model data
            
        Returns:
            list[TrainedModel]: List of new instances created from the dictionaries
        """
        return [TrainedModel.from_dict(d) for d in ds]
