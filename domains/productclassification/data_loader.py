#!/usr/bin/env python3

import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
import random
from datetime import datetime, timedelta
import re

from domains.repository.product_repository import ProductRepository
from domains.repository.bas_repository import BasRepository
from domains.productclassification.product_classifier_model import TextPreprocessor, ClassificationConfig


class ProductDataLoader:
    """Data loader for product classification training"""
    
    def __init__(self, config: ClassificationConfig = None):
        self.config = config or ClassificationConfig()
        self.product_repo = ProductRepository.new_instance()
        self.bas_repo = BasRepository.new_instance()
        self.preprocessor = TextPreprocessor(self.config)
        
        # Load categories and tags
        self.preprocessor.load_categories()
        
    def load_product_data(self, customer_ids: List[int] = None, limit: int = 10000) -> List[Dict[str, Any]]:
        """Load product data from database"""
        products = []
        
        if customer_ids is None:
            # Get all active customer IDs
            customer_data = self.product_repo.get_customer_ids()
            customer_ids = [item['id'] for item in customer_data[:10]]  # Limit to first 10 customers
        
        print(f"Loading product data for {len(customer_ids)} customers...")
        
        for customer_id in customer_ids:
            try:
                # Get products for this customer
                customer_products = self.product_repo.get_product_optimization_by_strategy(
                    customer_id, ['elasticPricing'], limit // len(customer_ids), 0
                )
                
                for product in customer_products:
                    if product.get('title') and product.get('asin'):
                        products.append({
                            'customer_id': customer_id,
                            'asin': product.get('asin'),
                            'sku': product.get('sku'),
                            'title': product.get('title'),
                            'marketplace_id': product.get('marketplace_id'),
                            'stock': product.get('stock', 0),
                            'local_stock': product.get('local_stock', 0)
                        })
                
                print(f"Loaded {len(customer_products)} products for customer {customer_id}")
                
            except Exception as e:
                print(f"Error loading products for customer {customer_id}: {e}")
                continue
        
        print(f"Total products loaded: {len(products)}")
        return products
    
    def generate_synthetic_training_data(self, num_samples: int = 5000) -> List[Dict[str, Any]]:
        """Generate synthetic training data based on category tags"""
        synthetic_data = []
        
        # Templates for generating realistic product titles
        title_templates = [
            "{brand} {product} {color} {size}",
            "{product} for {use_case} - {feature}",
            "{brand} {product} with {feature}",
            "{color} {product} {size} {material}",
            "Professional {product} {feature}",
            "{product} Set - {feature} {color}",
            "{brand} {product} - {use_case} {size}",
            "Premium {material} {product} {color}",
            "{product} Kit with {feature}",
            "{size} {color} {product} for {use_case}"
        ]
        
        # Common attributes
        brands = ["Amazon", "Generic", "Pro", "Elite", "Premium", "Basic", "Advanced", "Standard"]
        colors = ["Black", "White", "Blue", "Red", "Green", "Gray", "Silver", "Gold", "Brown"]
        sizes = ["Small", "Medium", "Large", "XL", "Mini", "Compact", "Full Size", "Standard"]
        materials = ["Plastic", "Metal", "Wood", "Fabric", "Leather", "Silicone", "Steel", "Aluminum"]
        features = ["Waterproof", "Wireless", "Portable", "Durable", "Lightweight", "Heavy Duty", "Adjustable"]
        use_cases = ["Home", "Office", "Outdoor", "Travel", "Professional", "Kids", "Adults", "Kitchen"]
        
        for category, tags in self.preprocessor.tags_by_category.items():
            if not tags:
                continue
                
            # Generate samples for this category
            samples_per_category = max(1, num_samples // len(self.preprocessor.tags_by_category))
            
            for _ in range(samples_per_category):
                # Pick a random tag as the main product
                main_tag = random.choice(tags)
                
                # Generate title using template
                template = random.choice(title_templates)
                
                title = template.format(
                    brand=random.choice(brands),
                    product=main_tag,
                    color=random.choice(colors),
                    size=random.choice(sizes),
                    material=random.choice(materials),
                    feature=random.choice(features),
                    use_case=random.choice(use_cases)
                )
                
                # Clean up the title
                title = re.sub(r'\s+', ' ', title).strip()
                
                synthetic_data.append({
                    'title': title,
                    'category': category,
                    'asin': f"B{random.randint(10000000, 99999999):08d}",
                    'sku': f"SKU-{random.randint(1000, 9999)}",
                    'marketplace_id': random.choice(['A1PA6795UKMFR9', 'ATVPDKIKX0DER', 'A1F83G8C2ARO7P']),
                    'synthetic': True
                })
        
        # Shuffle the data
        random.shuffle(synthetic_data)
        return synthetic_data[:num_samples]
    
    def augment_real_data_with_categories(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Augment real product data with predicted categories"""
        augmented_data = []
        
        for product in products:
            title = product.get('title', '')
            if not title:
                continue
            
            # Try to predict category using rule-based approach
            predicted_category = self.preprocessor.predict_category_from_tags(title)
            
            if predicted_category:
                augmented_data.append({
                    'title': title,
                    'category': predicted_category,
                    'asin': product.get('asin'),
                    'sku': product.get('sku'),
                    'marketplace_id': product.get('marketplace_id'),
                    'customer_id': product.get('customer_id'),
                    'synthetic': False
                })
        
        return augmented_data
    
    def create_training_dataset(self, 
                              use_real_data: bool = True,
                              use_synthetic_data: bool = True,
                              real_data_limit: int = 5000,
                              synthetic_data_limit: int = 10000,
                              customer_ids: List[int] = None) -> Tuple[List[str], List[str]]:
        """Create complete training dataset"""
        
        all_data = []
        
        # Load real data if requested
        if use_real_data:
            print("Loading real product data...")
            real_products = self.load_product_data(customer_ids, real_data_limit)
            augmented_real_data = self.augment_real_data_with_categories(real_products)
            all_data.extend(augmented_real_data)
            print(f"Added {len(augmented_real_data)} real products with predicted categories")
        
        # Generate synthetic data if requested
        if use_synthetic_data:
            print("Generating synthetic training data...")
            synthetic_data = self.generate_synthetic_training_data(synthetic_data_limit)
            all_data.extend(synthetic_data)
            print(f"Added {len(synthetic_data)} synthetic products")
        
        if not all_data:
            raise ValueError("No training data available. Enable real_data or synthetic_data.")
        
        # Extract titles and categories
        titles = [item['title'] for item in all_data]
        categories = [item['category'] for item in all_data]
        
        # Filter out categories not in our main category list
        valid_data = []
        for title, category in zip(titles, categories):
            if category in self.preprocessor.category_to_idx:
                valid_data.append((title, category))
        
        if not valid_data:
            raise ValueError("No valid training data found with matching categories.")
        
        titles, categories = zip(*valid_data)
        
        print(f"Final training dataset: {len(titles)} samples")
        print(f"Categories distribution:")
        category_counts = {}
        for cat in categories:
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        for cat, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {cat}: {count} samples")
        
        return list(titles), list(categories)
    
    def create_test_samples(self) -> List[Dict[str, str]]:
        """Create test samples for evaluation"""
        test_samples = [
            {"title": "Apple iPhone 13 Pro Max 128GB Blue", "expected_category": "Consumer Electronics"},
            {"title": "Nike Air Max 270 Running Shoes Black Size 10", "expected_category": "Footwear"},
            {"title": "Samsung 55-inch 4K Smart TV", "expected_category": "Consumer Electronics"},
            {"title": "Baby Stroller with Car Seat Travel System", "expected_category": "Baby Products"},
            {"title": "Kitchen Knife Set Professional Chef Knives", "expected_category": "Home and Kitchen"},
            {"title": "Wireless Bluetooth Headphones Noise Cancelling", "expected_category": "Consumer Electronics"},
            {"title": "Men's Cotton T-Shirt Blue Large", "expected_category": "Clothing and Accessories"},
            {"title": "Laptop Computer Bag Messenger Style", "expected_category": "Backpacks and Handbags"},
            {"title": "Automotive Car Battery 12V", "expected_category": "Automotive and Powersports"},
            {"title": "Garden Hose 50ft Heavy Duty", "expected_category": "Lawn and Garden"},
            {"title": "Office Chair Ergonomic Black Leather", "expected_category": "Furniture"},
            {"title": "Protein Powder Whey Vanilla 5lb", "expected_category": "Beauty, Health and Personal Care"},
            {"title": "Dog Food Dry Adult Large Breed", "expected_category": "Pet Products"},
            {"title": "Power Drill Cordless 18V with Battery", "expected_category": "Tools and Home Improvement"},
            {"title": "Board Game Strategy Family Fun", "expected_category": "Toys and Games"},
            {"title": "Sunglasses Polarized UV Protection", "expected_category": "Eyewear"},
            {"title": "Camping Tent 4 Person Waterproof", "expected_category": "Sports and Outdoors"},
            {"title": "Coffee Maker Single Serve K-Cup", "expected_category": "Home and Kitchen"},
            {"title": "Watch Smart Fitness Tracker", "expected_category": "Watches"},
            {"title": "Mattress Memory Foam Queen Size", "expected_category": "Mattresses"}
        ]
        
        return test_samples


def create_sample_training_data():
    """Create sample training data for testing"""
    loader = ProductDataLoader()
    
    # Create a small dataset for testing
    titles, categories = loader.create_training_dataset(
        use_real_data=False,  # Use only synthetic data for testing
        use_synthetic_data=True,
        synthetic_data_limit=1000
    )
    
    return titles, categories


if __name__ == "__main__":
    # Test the data loader
    loader = ProductDataLoader()
    
    # Test synthetic data generation
    print("Testing synthetic data generation...")
    titles, categories = create_sample_training_data()
    
    print(f"Generated {len(titles)} training samples")
    print("\nSample titles:")
    for i in range(min(10, len(titles))):
        print(f"  {categories[i]}: {titles[i]}")
    
    # Test real data loading (if available)
    try:
        print("\nTesting real data loading...")
        real_products = loader.load_product_data(limit=10)
        print(f"Loaded {len(real_products)} real products")
        
        if real_products:
            print("\nSample real products:")
            for i, product in enumerate(real_products[:5]):
                print(f"  {product.get('title', 'No title')}")
    except Exception as e:
        print(f"Could not load real data: {e}")
