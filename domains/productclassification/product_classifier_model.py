#!/usr/bin/env python3

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from dataclasses import dataclass
import re
from collections import Counter
import pickle
import os
from datetime import datetime, timezone

from lib.torch_utils import machine_device


@dataclass
class ClassificationConfig:
    """Configuration for product classification model"""
    
    # Model architecture
    vocab_size: int = 10000
    embed_dim: int = 128
    hidden_dim: int = 256
    num_classes: int = 37  # Number of main categories
    max_sequence_length: int = 64
    
    # Training parameters
    learning_rate: float = 0.001
    batch_size: int = 32
    num_epochs: int = 50
    dropout_rate: float = 0.3
    
    # Text processing
    min_word_freq: int = 2
    max_features: int = 10000
    
    # Model paths
    model_path: str = "models/product_classifier.pt"
    vocab_path: str = "models/product_classifier_vocab.pkl"
    categories_path: str = "test/catalog/categories.txt"
    tags_path: str = "test/catalog/categories_tags.txt"


class TextPreprocessor:
    """Text preprocessing for product titles"""
    
    def __init__(self, config: ClassificationConfig):
        self.config = config
        self.word_to_idx = {"<PAD>": 0, "<UNK>": 1}
        self.idx_to_word = {0: "<PAD>", 1: "<UNK>"}
        self.vocab_size = 2
        self.categories = []
        self.category_to_idx = {}
        self.tags_by_category = {}
        
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces and alphanumeric
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize(self, text: str) -> List[str]:
        """Tokenize text into words"""
        cleaned_text = self.clean_text(text)
        return cleaned_text.split()
    
    def build_vocabulary(self, texts: List[str]):
        """Build vocabulary from training texts"""
        word_counts = Counter()
        
        for text in texts:
            tokens = self.tokenize(text)
            word_counts.update(tokens)
        
        # Add words that appear at least min_word_freq times
        for word, count in word_counts.most_common(self.config.max_features - 2):
            if count >= self.config.min_word_freq:
                self.word_to_idx[word] = self.vocab_size
                self.idx_to_word[self.vocab_size] = word
                self.vocab_size += 1
    
    def load_categories(self):
        """Load categories and tags from files"""
        # Load main categories
        try:
            with open(self.config.categories_path, 'r', encoding='utf-8') as f:
                self.categories = [line.strip() for line in f if line.strip()]
            
            # Create category to index mapping
            self.category_to_idx = {cat: idx for idx, cat in enumerate(self.categories)}
            
            # Load category tags
            current_category = None
            with open(self.config.tags_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if line.endswith(':'):
                        # This is a category header
                        current_category = line[:-1]
                        if current_category not in self.tags_by_category:
                            self.tags_by_category[current_category] = []
                    else:
                        # This is a tag
                        if current_category and current_category in self.category_to_idx:
                            self.tags_by_category[current_category].append(line)
                            
        except FileNotFoundError as e:
            print(f"Warning: Could not load category files: {e}")
            # Fallback to default categories
            self.categories = [f"Category_{i}" for i in range(self.config.num_classes)]
            self.category_to_idx = {cat: idx for idx, cat in enumerate(self.categories)}
    
    def text_to_sequence(self, text: str) -> List[int]:
        """Convert text to sequence of token indices"""
        tokens = self.tokenize(text)
        sequence = []
        
        for token in tokens:
            idx = self.word_to_idx.get(token, 1)  # 1 is <UNK>
            sequence.append(idx)
        
        # Pad or truncate to max_sequence_length
        if len(sequence) > self.config.max_sequence_length:
            sequence = sequence[:self.config.max_sequence_length]
        else:
            sequence.extend([0] * (self.config.max_sequence_length - len(sequence)))
        
        return sequence
    
    def predict_category_from_tags(self, title: str) -> Optional[str]:
        """Predict category based on tag matching"""
        title_lower = title.lower()
        
        # Score each category based on tag matches
        category_scores = {}
        
        for category, tags in self.tags_by_category.items():
            score = 0
            for tag in tags:
                tag_words = tag.lower().split()
                # Check if all words in tag appear in title
                if all(word in title_lower for word in tag_words):
                    score += len(tag_words)  # Longer matches get higher scores
            
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            # Return category with highest score
            return max(category_scores, key=category_scores.get)
        
        return None
    
    def save_vocabulary(self, path: str):
        """Save vocabulary to file"""
        vocab_data = {
            'word_to_idx': self.word_to_idx,
            'idx_to_word': self.idx_to_word,
            'vocab_size': self.vocab_size,
            'categories': self.categories,
            'category_to_idx': self.category_to_idx,
            'tags_by_category': self.tags_by_category
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'wb') as f:
            pickle.dump(vocab_data, f)
    
    def load_vocabulary(self, path: str):
        """Load vocabulary from file"""
        with open(path, 'rb') as f:
            vocab_data = pickle.load(f)
        
        self.word_to_idx = vocab_data['word_to_idx']
        self.idx_to_word = vocab_data['idx_to_word']
        self.vocab_size = vocab_data['vocab_size']
        self.categories = vocab_data['categories']
        self.category_to_idx = vocab_data['category_to_idx']
        self.tags_by_category = vocab_data['tags_by_category']


class ProductClassifierNN(nn.Module):
    """Neural network for product classification"""
    
    def __init__(self, config: ClassificationConfig, vocab_size: int):
        super().__init__()
        self.config = config
        self.vocab_size = vocab_size
        
        # Embedding layer
        self.embedding = nn.Embedding(vocab_size, config.embed_dim, padding_idx=0)
        
        # Bidirectional LSTM for sequence processing
        self.lstm = nn.LSTM(
            config.embed_dim, 
            config.hidden_dim // 2,  # Divide by 2 because bidirectional
            batch_first=True,
            bidirectional=True,
            dropout=config.dropout_rate if config.dropout_rate > 0 else 0,
            num_layers=2
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            config.hidden_dim, 
            num_heads=8, 
            dropout=config.dropout_rate,
            batch_first=True
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim // 2, config.num_classes)
        )
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.uniform_(module.weight, -0.1, 0.1)
                if module.padding_idx is not None:
                    module.weight.data[module.padding_idx].fill_(0)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None):
        """Forward pass"""
        batch_size, seq_len = input_ids.shape
        
        # Embedding
        embedded = self.embedding(input_ids)  # (batch_size, seq_len, embed_dim)
        
        # LSTM
        lstm_out, (hidden, cell) = self.lstm(embedded)  # (batch_size, seq_len, hidden_dim)
        
        # Self-attention
        if attention_mask is not None:
            # Convert attention mask to the format expected by MultiheadAttention
            attention_mask = attention_mask.float()
            attention_mask = attention_mask.masked_fill(attention_mask == 0, float('-inf'))
            attention_mask = attention_mask.masked_fill(attention_mask == 1, 0.0)
        
        attended_out, _ = self.attention(lstm_out, lstm_out, lstm_out, key_padding_mask=attention_mask)
        
        # Global max pooling
        pooled = torch.max(attended_out, dim=1)[0]  # (batch_size, hidden_dim)
        
        # Classification
        logits = self.classifier(pooled)  # (batch_size, num_classes)
        
        return logits
    
    def predict(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Make predictions"""
        self.eval()
        with torch.no_grad():
            logits = self.forward(input_ids, attention_mask)
            probabilities = F.softmax(logits, dim=-1)
            return probabilities


class ProductClassifierTrainer:
    """Trainer for product classification model"""

    def __init__(self, config: ClassificationConfig = None):
        self.config = config or ClassificationConfig()
        self.preprocessor = TextPreprocessor(self.config)
        self.model = None
        self.device = machine_device()

        # Training metrics
        self.training_history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }

    def prepare_data(self, titles: List[str], categories: List[str]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Prepare training data"""
        # Build vocabulary if not already built
        if self.preprocessor.vocab_size <= 2:
            self.preprocessor.build_vocabulary(titles)
            self.preprocessor.load_categories()

        # Convert texts to sequences
        sequences = []
        attention_masks = []
        labels = []

        for title, category in zip(titles, categories):
            # Convert title to sequence
            sequence = self.preprocessor.text_to_sequence(title)
            sequences.append(sequence)

            # Create attention mask (1 for real tokens, 0 for padding)
            attention_mask = [1 if token_id != 0 else 0 for token_id in sequence]
            attention_masks.append(attention_mask)

            # Convert category to label
            label = self.preprocessor.category_to_idx.get(category, 0)
            labels.append(label)

        # Convert to tensors
        input_ids = torch.tensor(sequences, dtype=torch.long)
        attention_mask = torch.tensor(attention_masks, dtype=torch.long)
        labels = torch.tensor(labels, dtype=torch.long)

        return input_ids, attention_mask, labels

    def create_data_loader(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                          labels: torch.Tensor, batch_size: int, shuffle: bool = True):
        """Create data loader"""
        dataset = torch.utils.data.TensorDataset(input_ids, attention_mask, labels)
        return torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)

    def train_epoch(self, train_loader, optimizer, criterion):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0

        for batch_idx, (input_ids, attention_mask, labels) in enumerate(train_loader):
            input_ids = input_ids.to(self.device)
            attention_mask = attention_mask.to(self.device)
            labels = labels.to(self.device)

            # Forward pass
            optimizer.zero_grad()
            logits = self.model(input_ids, attention_mask)
            loss = criterion(logits, labels)

            # Backward pass
            loss.backward()
            optimizer.step()

            # Statistics
            total_loss += loss.item()
            predictions = torch.argmax(logits, dim=-1)
            correct_predictions += (predictions == labels).sum().item()
            total_predictions += labels.size(0)

            if batch_idx % 100 == 0:
                print(f'Batch {batch_idx}, Loss: {loss.item():.4f}')

        avg_loss = total_loss / len(train_loader)
        accuracy = correct_predictions / total_predictions

        return avg_loss, accuracy

    def validate_epoch(self, val_loader, criterion):
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0

        with torch.no_grad():
            for input_ids, attention_mask, labels in val_loader:
                input_ids = input_ids.to(self.device)
                attention_mask = attention_mask.to(self.device)
                labels = labels.to(self.device)

                # Forward pass
                logits = self.model(input_ids, attention_mask)
                loss = criterion(logits, labels)

                # Statistics
                total_loss += loss.item()
                predictions = torch.argmax(logits, dim=-1)
                correct_predictions += (predictions == labels).sum().item()
                total_predictions += labels.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct_predictions / total_predictions

        return avg_loss, accuracy

    def train(self, titles: List[str], categories: List[str], val_split: float = 0.2):
        """Train the model"""
        print("Starting product classification training...")

        # Prepare data
        print("Preparing data...")
        input_ids, attention_mask, labels = self.prepare_data(titles, categories)

        # Split data
        total_size = len(titles)
        val_size = int(total_size * val_split)
        train_size = total_size - val_size

        # Random split
        indices = torch.randperm(total_size)
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]

        train_input_ids = input_ids[train_indices]
        train_attention_mask = attention_mask[train_indices]
        train_labels = labels[train_indices]

        val_input_ids = input_ids[val_indices]
        val_attention_mask = attention_mask[val_indices]
        val_labels = labels[val_indices]

        # Create data loaders
        train_loader = self.create_data_loader(
            train_input_ids, train_attention_mask, train_labels,
            self.config.batch_size, shuffle=True
        )
        val_loader = self.create_data_loader(
            val_input_ids, val_attention_mask, val_labels,
            self.config.batch_size, shuffle=False
        )

        # Initialize model
        self.model = ProductClassifierNN(self.config, self.preprocessor.vocab_size)
        self.model.to(self.device)

        # Initialize optimizer and criterion
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.config.learning_rate)
        criterion = nn.CrossEntropyLoss()

        # Training loop
        print(f"Training on {train_size} samples, validating on {val_size} samples")
        best_val_accuracy = 0

        for epoch in range(self.config.num_epochs):
            print(f"\nEpoch {epoch + 1}/{self.config.num_epochs}")

            # Train
            train_loss, train_accuracy = self.train_epoch(train_loader, optimizer, criterion)

            # Validate
            val_loss, val_accuracy = self.validate_epoch(val_loader, criterion)

            # Store metrics
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_accuracy'].append(train_accuracy)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_accuracy'].append(val_accuracy)

            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}")

            # Save best model
            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy
                self.save_model()
                print(f"New best model saved with validation accuracy: {val_accuracy:.4f}")

        print(f"\nTraining completed. Best validation accuracy: {best_val_accuracy:.4f}")
        return self.training_history

    def save_model(self):
        """Save model and preprocessor"""
        os.makedirs(os.path.dirname(self.config.model_path), exist_ok=True)

        # Save model state
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'vocab_size': self.preprocessor.vocab_size,
            'training_history': self.training_history
        }, self.config.model_path)

        # Save preprocessor
        self.preprocessor.save_vocabulary(self.config.vocab_path)

        print(f"Model saved to {self.config.model_path}")
        print(f"Vocabulary saved to {self.config.vocab_path}")

    def load_model(self, model_path: str = None, vocab_path: str = None):
        """Load trained model"""
        model_path = model_path or self.config.model_path
        vocab_path = vocab_path or self.config.vocab_path

        # Load preprocessor
        self.preprocessor.load_vocabulary(vocab_path)

        # Load model
        checkpoint = torch.load(model_path, map_location=self.device)
        self.config = checkpoint['config']

        self.model = ProductClassifierNN(self.config, checkpoint['vocab_size'])
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()

        if 'training_history' in checkpoint:
            self.training_history = checkpoint['training_history']

        print(f"Model loaded from {model_path}")

    def predict_single(self, title: str) -> Dict[str, Any]:
        """Predict category for a single product title"""
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        # First try rule-based prediction using tags
        rule_based_category = self.preprocessor.predict_category_from_tags(title)

        # Prepare input
        sequence = self.preprocessor.text_to_sequence(title)
        attention_mask = [1 if token_id != 0 else 0 for token_id in sequence]

        input_ids = torch.tensor([sequence], dtype=torch.long).to(self.device)
        attention_mask_tensor = torch.tensor([attention_mask], dtype=torch.long).to(self.device)

        # Get model prediction
        probabilities = self.model.predict(input_ids, attention_mask_tensor)
        predicted_idx = torch.argmax(probabilities, dim=-1).item()
        confidence = probabilities[0][predicted_idx].item()

        predicted_category = self.preprocessor.categories[predicted_idx]

        # Get top 3 predictions
        top_probs, top_indices = torch.topk(probabilities[0], k=min(3, len(self.preprocessor.categories)))
        top_predictions = [
            {
                'category': self.preprocessor.categories[idx.item()],
                'confidence': prob.item()
            }
            for prob, idx in zip(top_probs, top_indices)
        ]

        return {
            'title': title,
            'predicted_category': predicted_category,
            'confidence': confidence,
            'rule_based_category': rule_based_category,
            'top_predictions': top_predictions,
            'method': 'hybrid' if rule_based_category else 'neural_network'
        }

    def predict_batch(self, titles: List[str]) -> List[Dict[str, Any]]:
        """Predict categories for multiple product titles"""
        return [self.predict_single(title) for title in titles]
