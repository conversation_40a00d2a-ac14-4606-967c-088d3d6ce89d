"""
Enhanced Price Prediction Model for Elastic Pricing

This model builds upon the existing architecture while incorporating:
- Temporal features from sales history
- Advanced embedding techniques for categorical variables
- Multi-head attention for feature relationships
- Improved loss functions for better price optimization
- Integration with existing database and API structure
"""

import sys
import os
from io import BytesIO
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime
import pandas as pd
import numpy as np

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split


def machine_device() -> torch.device:
    return torch.device("cuda" if torch.cuda.is_available() else "cpu")


class TemporalFeatureExtractor(nn.Module):
    """Extract temporal patterns from sales history data"""

    def __init__(self, input_dim: int, hidden_dim: int = 64):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # LSTM for temporal sequence modeling
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=2,
            batch_first=True,
            dropout=0.2,
            bidirectional=True
        )

        # Attention mechanism for temporal features
        self.temporal_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim * 2,  # bidirectional
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

        # Feature projection
        self.feature_projection = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )

    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_dim)
        lstm_out, (h_n, c_n) = self.lstm(x)

        # Apply attention to LSTM output
        attn_out, _ = self.temporal_attention(lstm_out, lstm_out, lstm_out)

        # Use the last timestep with attention
        temporal_features = self.feature_projection(attn_out[:, -1, :])

        return temporal_features


class EnhancedEmbeddingLayer(nn.Module):
    """Enhanced embedding layer with feature interactions"""

    def __init__(self, vocab_sizes: Dict[str, int], embed_dim: int = 32):
        super().__init__()
        self.embed_dim = embed_dim

        # Create embeddings for each categorical feature
        self.embeddings = nn.ModuleDict({
            name: nn.Embedding(vocab_size, embed_dim)
            for name, vocab_size in vocab_sizes.items()
        })

        # Feature interaction layer
        total_embed_dim = len(vocab_sizes) * embed_dim
        self.interaction_layer = nn.Sequential(
            nn.Linear(total_embed_dim, total_embed_dim // 2),
            nn.LayerNorm(total_embed_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(total_embed_dim // 2, embed_dim * 2)
        )

    def forward(self, categorical_features: Dict[str, torch.Tensor]):
        # Get embeddings for each categorical feature
        embedded_features = []
        for name, indices in categorical_features.items():
            if name in self.embeddings:
                embedded = self.embeddings[name](indices)
                embedded_features.append(embedded)

        # Concatenate all embeddings
        if embedded_features:
            combined_embeddings = torch.cat(embedded_features, dim=-1)
            # Apply feature interactions
            enhanced_embeddings = self.interaction_layer(combined_embeddings)
            return enhanced_embeddings
        else:
            # Return zero tensor if no categorical features
            batch_size = list(categorical_features.values())[0].size(0)
            return torch.zeros(batch_size, self.embed_dim * 2, device=machine_device())


class EnhancedPricePredictor(nn.Module):
    """
    Enhanced Price Prediction Model with temporal features and advanced architecture

    This model predicts optimal pricing parameters:
    - step_price_type: UP(1) or DOWN(0) price direction
    - step_point: PERCENT(1) or VALUE(0) adjustment type
    - step_price: magnitude of price adjustment
    - recommended_steps: number of optimization steps
    """

    def __init__(
        self,
        numeric_dim: int,
        vocab_sizes: Dict[str, int],
        embed_dim: int = 32,
        hidden_dim: int = 128,
        num_heads: int = 8,
        dropout_rate: float = 0.2
    ):
        super().__init__()

        self.numeric_dim = numeric_dim
        self.vocab_sizes = vocab_sizes
        self.embed_dim = embed_dim
        self.hidden_dim = hidden_dim

        # Enhanced embedding layer
        self.embedding_layer = EnhancedEmbeddingLayer(vocab_sizes, embed_dim)

        # Temporal feature extractor
        self.temporal_extractor = TemporalFeatureExtractor(
            input_dim=numeric_dim,
            hidden_dim=hidden_dim // 2
        )

        # Numeric feature processing
        self.numeric_processor = nn.Sequential(
            nn.Linear(numeric_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5)
        )

        # Multi-modal fusion layer
        fusion_input_dim = hidden_dim + (embed_dim * 2) + (hidden_dim // 2)
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_input_dim, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout_rate)
        )

        # Multi-head attention for feature relationships
        self.feature_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim * 2,
            num_heads=num_heads,
            dropout=dropout_rate * 0.5,
            batch_first=True
        )

        # Shared feature extraction
        self.shared_layers = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5)
        )

        # Specialized output heads
        head_dim = hidden_dim // 2

        # Binary classification heads
        self.price_type_head = self._create_classification_head(head_dim, 2)  # UP/DOWN
        self.step_point_head = self._create_classification_head(head_dim, 2)  # PERCENT/VALUE

        # Regression heads
        self.step_price_head = self._create_regression_head(head_dim)
        self.recommended_steps_head = self._create_regression_head(head_dim)
        # self.limit_head = self._create_regression_head(head_dim)

        # Initialize weights
        self._initialize_weights()

    def _create_classification_head(self, input_dim: int, num_classes: int):
        return nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.LayerNorm(input_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim // 2, num_classes)
        )

    def _create_regression_head(self, input_dim: int):
        return nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.LayerNorm(input_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim // 2, 1)
        )

    def _initialize_weights(self):
        """Initialize weights with better scaling to prevent extreme outputs"""
        for name, module in self.named_modules():
            if isinstance(module, nn.Linear):
                # Use smaller initialization for output heads to prevent extreme values
                if any(head_name in name for head_name in ['price_type_head', 'step_point_head', 'step_price_head', 'recommended_steps_head']):
                    nn.init.xavier_uniform_(module.weight, gain=0.1)  # Much smaller gain for output heads
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
                else:
                    nn.init.xavier_uniform_(module.weight, gain=nn.init.calculate_gain('relu'))
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.LayerNorm):
                nn.init.constant_(module.bias, 0)
                nn.init.constant_(module.weight, 1.0)
            elif isinstance(module, nn.Embedding):
                nn.init.xavier_uniform_(module.weight, gain=0.5)  # Smaller gain for embeddings

    def forward(
        self,
        numeric_features: torch.Tensor,
        categorical_features: Dict[str, torch.Tensor],
        temporal_sequence: Optional[torch.Tensor] = None
    ):
        batch_size = numeric_features.size(0)

        # Process numeric features
        numeric_processed = self.numeric_processor(numeric_features)

        # Process categorical features
        categorical_processed = self.embedding_layer(categorical_features)

        # Process temporal features if available
        if temporal_sequence is not None:
            temporal_processed = self.temporal_extractor(temporal_sequence)
        else:
            temporal_processed = torch.zeros(
                batch_size, self.hidden_dim // 2,
                device=numeric_features.device
            )

        # Fuse all features
        fused_features = torch.cat([
            numeric_processed,
            categorical_processed,
            temporal_processed
        ], dim=-1)

        fused_features = self.fusion_layer(fused_features)

        # Apply attention mechanism
        fused_features = fused_features.unsqueeze(1)  # Add sequence dimension
        attended_features, _ = self.feature_attention(
            fused_features, fused_features, fused_features
        )
        attended_features = attended_features.squeeze(1)  # Remove sequence dimension

        # Shared feature extraction
        shared_features = self.shared_layers(attended_features)

        # Generate predictions
        price_type_logits = self.price_type_head(shared_features)
        step_point_logits = self.step_point_head(shared_features)
        step_price = self.step_price_head(shared_features).squeeze(-1)
        recommended_steps = self.recommended_steps_head(shared_features).squeeze(-1)
        # limit = self.limit_head(shared_features).squeeze(-1)

        # Apply appropriate activations with better scaling
        # For step_price: use sigmoid to get 0-1, then scale to reasonable range (0.5-20)
        step_price = torch.sigmoid(step_price) * 19.5 + 0.5  # Range 0.5-20
        recommended_steps = torch.clamp(torch.sigmoid(recommended_steps) * 4 + 1, min=1.0, max=5.0)  # Range 1-5

        # Replace any NaN values with reasonable defaults
        step_price = torch.where(torch.isnan(step_price), torch.tensor(1.0, device=step_price.device), step_price)
        recommended_steps = torch.where(torch.isnan(recommended_steps), torch.tensor(2.0, device=recommended_steps.device), recommended_steps)

        return {
            'price_type_logits': price_type_logits,
            'step_point_logits': step_point_logits,
            'step_price': step_price,
            'recommended_steps': recommended_steps,
            # 'limit': limit
        }


class EnhancedLossFunction(nn.Module):
    """
    Enhanced loss function for multi-task learning with adaptive weighting
    """

    def __init__(self, weights: Optional[Dict[str, float]] = None):
        super().__init__()

        # Balanced weights for different loss components
        self.weights = weights or {
            'price_type': 1.5,      # Slightly higher for important binary classification
            'step_point': 1.5,      # Slightly higher for important binary classification
            'step_price': 1.0,      # Reduced from 2.0 for better balance
            'recommended_steps': 1.0, # Reduced from 1.5 for better balance
        }

        # Loss functions
        self.ce_loss = nn.CrossEntropyLoss()
        self.mse_loss = nn.MSELoss()
        self.smooth_l1_loss = nn.SmoothL1Loss()

    def forward(self, predictions: Dict[str, torch.Tensor], targets: Dict[str, torch.Tensor]):
        losses = {}

        # Classification losses
        losses['price_type'] = self.ce_loss(
            predictions['price_type_logits'],
            targets['price_type'].long()
        )

        losses['step_point'] = self.ce_loss(
            predictions['step_point_logits'],
            targets['step_point'].long()
        )

        # Regression losses with NaN handling
        step_price_pred = predictions['step_price']
        step_price_target = targets['step_price']

        # Replace NaN values with zeros
        step_price_pred = torch.where(torch.isnan(step_price_pred), torch.zeros_like(step_price_pred), step_price_pred)
        step_price_target = torch.where(torch.isnan(step_price_target), torch.zeros_like(step_price_target), step_price_target)

        losses['step_price'] = self.mse_loss(step_price_pred, step_price_target)

        recommended_steps_pred = predictions['recommended_steps']
        recommended_steps_target = targets['recommended_steps']

        # Replace NaN values with ones (default step count)
        recommended_steps_pred = torch.where(torch.isnan(recommended_steps_pred), torch.ones_like(recommended_steps_pred), recommended_steps_pred)
        recommended_steps_target = torch.where(torch.isnan(recommended_steps_target), torch.ones_like(recommended_steps_target), recommended_steps_target)

        losses['recommended_steps'] = self.smooth_l1_loss(recommended_steps_pred, recommended_steps_target)

        # limit_pred = predictions['limit']
        # limit_target = targets['limit']

        # Replace NaN values with ones (default limit)
        # limit_pred = torch.where(torch.isnan(limit_pred), torch.ones_like(limit_pred), limit_pred)
        # limit_target = torch.where(torch.isnan(limit_target), torch.ones_like(limit_target), limit_target)

        # losses['limit'] = self.smooth_l1_loss(limit_pred, limit_target)

        # Check for NaN in losses and replace with zero
        for key in losses:
            if torch.isnan(losses[key]):
                losses[key] = torch.tensor(0.0, device=losses[key].device, requires_grad=True)

        # Weighted total loss
        total_loss = sum(
            self.weights[key] * loss
            for key, loss in losses.items()
        )

        # Final NaN check
        if torch.isnan(total_loss):
            total_loss = torch.tensor(0.0, device=total_loss.device, requires_grad=True)

        return total_loss, losses


class SalesDataPreprocessor:
    """
    Advanced data preprocessing pipeline for sales history data
    """

    def __init__(self):
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.is_fitted = False
        self.feature_names = None  # Store feature names to avoid sklearn warnings

        # Define feature columns
        self.categorical_features = ['asin', 'sku', 'order_marketplace_id']
        self.numeric_features = [
            'total_quantity', 'total_price', 'prev_total_quantity',
            'prev_total_price', 'price_per_quantity', 'prev_price_per_quantity',
            'delta_quantity', 'delta_price_per_quantity'
        ]
        self.target_features = [
            'step_price_type', 'step_point', 'step_price',
            'recommended_steps', 
            # 'limit'
        ]

    def fit(self, df: pd.DataFrame):
        """Fit the preprocessor on training data"""

        # Fit label encoders for categorical features
        for feature in self.categorical_features:
            if feature in df.columns:
                le = LabelEncoder()
                le.fit(df[feature].astype(str))
                self.label_encoders[feature] = le

        # Fit scaler on numeric features
        numeric_data = df[self.numeric_features].fillna(0)
        # Store feature names and convert to numpy array to avoid sklearn warnings
        self.feature_names = numeric_data.columns.tolist()
        self.scaler.fit(numeric_data.values)  # Use .values to remove feature names

        self.is_fitted = True
        return self

    def transform(self, df: pd.DataFrame) -> Tuple[Dict[str, Any], Dict[str, torch.Tensor]]:
        """Transform data for model input"""

        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")

        # Process categorical features
        categorical_data = {}
        vocab_sizes = {}

        for feature in self.categorical_features:
            if feature in df.columns and feature in self.label_encoders:
                encoded = self.label_encoders[feature].transform(df[feature].astype(str))
                categorical_data[feature] = torch.tensor(encoded, dtype=torch.long)
                vocab_sizes[feature] = len(self.label_encoders[feature].classes_)

        # Process numeric features - handle missing columns gracefully
        available_numeric_features = [f for f in self.numeric_features if f in df.columns]
        if len(available_numeric_features) != len(self.numeric_features):
            print(f"Warning: Missing numeric features: {set(self.numeric_features) - set(available_numeric_features)}")

        numeric_data = df[available_numeric_features].fillna(0)

        # Pad with zeros for missing features to maintain consistent shape
        if len(available_numeric_features) < len(self.numeric_features):
            missing_count = len(self.numeric_features) - len(available_numeric_features)
            padding = np.zeros((len(numeric_data), missing_count))
            numeric_array = np.concatenate([numeric_data.values, padding], axis=1)
        else:
            numeric_array = numeric_data.values

        # Use .values to avoid sklearn feature name warnings
        numeric_scaled = self.scaler.transform(numeric_array)
        numeric_tensor = torch.tensor(numeric_scaled, dtype=torch.float32)

        # Process targets if available
        targets = {}
        if all(col in df.columns for col in self.target_features):
            targets = {
                'price_type': torch.tensor(df['step_price_type'].values, dtype=torch.float32),
                'step_point': torch.tensor(df['step_point'].values, dtype=torch.float32),
                'step_price': torch.tensor(df['step_price'].values, dtype=torch.float32),
                'recommended_steps': torch.tensor(df['recommended_steps'].values, dtype=torch.float32),
                # 'limit': torch.tensor(df['limit'].values, dtype=torch.float32)
            }

        return {
            'numeric_features': numeric_tensor,
            'categorical_features': categorical_data,
            'vocab_sizes': vocab_sizes,
            'targets': targets
        }

    def fit_transform(self, df: pd.DataFrame) -> Tuple[Dict[str, Any], Dict[str, torch.Tensor]]:
        """Fit and transform in one step"""
        return self.fit(df).transform(df)

    def create_temporal_sequences(
        self,
        df: pd.DataFrame,
        sequence_length: int = 7,
        group_cols: List[str] = None
    ) -> Optional[torch.Tensor]:
        """
        Create temporal sequences for LSTM processing

        Args:
            df: DataFrame with temporal data
            sequence_length: Length of sequences to create
            group_cols: Columns to group by (default: ['asin', 'sku', 'order_marketplace_id'])

        Returns:
            Tensor of shape (batch_size, sequence_length, num_features)
        """

        if group_cols is None:
            group_cols = ['asin', 'sku', 'order_marketplace_id']

        # Sort by date within each group
        df_sorted = df.sort_values(group_cols + ['order_purchase_date'])

        sequences = []
        numeric_cols = [col for col in self.numeric_features if col in df.columns]

        for group_key, group_df in df_sorted.groupby(group_cols):
            if len(group_df) >= sequence_length:
                # Create sequences for this group
                group_numeric = group_df[numeric_cols].fillna(0).values

                for i in range(len(group_numeric) - sequence_length + 1):
                    sequence = group_numeric[i:i + sequence_length]
                    sequences.append(sequence)

        if sequences:
            return torch.tensor(np.array(sequences), dtype=torch.float32)
        else:
            return None


class EnhancedModelTrainer:
    """
    Advanced training system with validation, early stopping, and model evaluation
    """

    def __init__(
        self,
        model: EnhancedPricePredictor,
        loss_function: EnhancedLossFunction,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5
    ):
        self.model = model
        self.loss_function = loss_function
        self.device = machine_device()

        # Move model to device
        self.model.to(self.device)

        # Optimizer with weight decay for regularization
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.999)
        )

        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=5
        )

        # Training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': []
        }

    def train_epoch(self, train_loader):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        for batch in train_loader:
            self.optimizer.zero_grad()

            # Move batch to device
            numeric_features = batch['numeric_features'].to(self.device)
            categorical_features = {
                k: v.to(self.device) for k, v in batch['categorical_features'].items()
            }
            targets = {
                k: v.to(self.device) for k, v in batch['targets'].items()
            }

            # Forward pass
            predictions = self.model(numeric_features, categorical_features)

            # Calculate loss
            loss, _ = self.loss_function(predictions, targets)

            # Backward pass
            loss.backward()

            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

        return total_loss / num_batches

    def validate_epoch(self, val_loader):
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in val_loader:
                # Move batch to device
                numeric_features = batch['numeric_features'].to(self.device)
                categorical_features = {
                    k: v.to(self.device) for k, v in batch['categorical_features'].items()
                }
                targets = {
                    k: v.to(self.device) for k, v in batch['targets'].items()
                }

                # Forward pass
                predictions = self.model(numeric_features, categorical_features)

                # Calculate loss
                loss, _ = self.loss_function(predictions, targets)

                total_loss += loss.item()
                num_batches += 1

                # Store predictions and targets for metrics
                all_predictions.append(predictions)
                all_targets.append(targets)

        avg_loss = total_loss / num_batches
        metrics = self._calculate_metrics(all_predictions, all_targets)

        return avg_loss, metrics

    def _calculate_metrics(self, predictions_list, targets_list):
        """Calculate evaluation metrics"""
        # Concatenate all predictions and targets
        all_pred_price_type = torch.cat([p['price_type_logits'] for p in predictions_list])
        all_pred_step_point = torch.cat([p['step_point_logits'] for p in predictions_list])
        all_pred_step_price = torch.cat([p['step_price'] for p in predictions_list])

        all_target_price_type = torch.cat([t['price_type'] for t in targets_list])
        all_target_step_point = torch.cat([t['step_point'] for t in targets_list])
        all_target_step_price = torch.cat([t['step_price'] for t in targets_list])

        # Calculate accuracies for classification tasks
        price_type_acc = (
            torch.argmax(all_pred_price_type, dim=1) == all_target_price_type.long()
        ).float().mean().item()

        step_point_acc = (
            torch.argmax(all_pred_step_point, dim=1) == all_target_step_point.long()
        ).float().mean().item()

        # Calculate MAE for regression tasks
        step_price_mae = torch.abs(all_pred_step_price - all_target_step_price).mean().item()

        return {
            'price_type_accuracy': price_type_acc,
            'step_point_accuracy': step_point_acc,
            'step_price_mae': step_price_mae
        }

    def train(
        self,
        train_loader,
        val_loader,
        num_epochs: int = 100,
        patience: int = 15,
        min_delta: float = 1e-4
    ):
        """
        Train the model with early stopping

        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            num_epochs: Maximum number of epochs
            patience: Early stopping patience
            min_delta: Minimum improvement threshold
        """

        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        print(f"Starting training for {num_epochs} epochs...")
        print(f"Device: {self.device}")

        for epoch in range(num_epochs):
            # Training phase
            train_loss = self.train_epoch(train_loader)

            # Validation phase
            val_loss, val_metrics = self.validate_epoch(val_loader)

            # Update learning rate
            self.scheduler.step(val_loss)

            # Store history
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['val_metrics'].append(val_metrics)

            # Early stopping check
            if val_loss < best_val_loss - min_delta:
                best_val_loss = val_loss
                best_model_state = self.model.state_dict().copy()
                patience_counter = 0
                print(f"Epoch {epoch+1}: New best model (val_loss: {val_loss:.4f})")
            else:
                patience_counter += 1

            # Print progress
            # if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}")
            print(f"  Train Loss: {train_loss:.4f}")
            print(f"  Val Loss: {val_loss:.4f}")
            print(f"  Price Type Acc: {val_metrics['price_type_accuracy']:.4f}")
            print(f"  Step Point Acc: {val_metrics['step_point_accuracy']:.4f}")
            print(f"  Step Price MAE: {val_metrics['step_price_mae']:.4f}")
            print(f"  Learning Rate: {self.optimizer.param_groups[0]['lr']:.6f}")

            # Early stopping
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

        # Load best model
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            print(f"Loaded best model with validation loss: {best_val_loss:.4f}")

        return self.history


def save_enhanced_model(
    model: EnhancedPricePredictor,
    preprocessor: SalesDataPreprocessor,
    path: str,
    model_info: Dict[str, Any] = None
):
    """Save the enhanced model with preprocessor"""

    model_state = {
        'model_state_dict': model.state_dict(),
        'model_config': {
            'numeric_dim': model.numeric_dim,
            'vocab_sizes': model.vocab_sizes,
            'embed_dim': model.embed_dim,
            'hidden_dim': model.hidden_dim
        },
        'preprocessor_state': {
            'label_encoders': {
                name: {
                    'classes_': le.classes_.tolist(),
                    'class_count': len(le.classes_)
                }
                for name, le in preprocessor.label_encoders.items()
            },
            'scaler_mean': preprocessor.scaler.mean_.tolist(),
            'scaler_scale': preprocessor.scaler.scale_.tolist(),
            'numeric_features': preprocessor.numeric_features,
            'categorical_features': preprocessor.categorical_features
        },
        'model_version': 'enhanced_v1',
        'created_at': datetime.now().isoformat(),
        'model_info': model_info or {}
    }

    torch.save(model_state, path)
    print(f"Enhanced model saved to {path}")


def load_enhanced_model(path: str = None, model_dump:bytes = None) -> Tuple[EnhancedPricePredictor, SalesDataPreprocessor]:
    """Load the enhanced model with preprocessor"""

    try:
        if model_dump is not None:
            state = torch.load(BytesIO(model_dump), map_location=machine_device())
        else:
            state = torch.load(path, map_location=machine_device())

        # Reconstruct model
        config = state['model_config']
        model = EnhancedPricePredictor(
            numeric_dim=config['numeric_dim'],
            vocab_sizes=config['vocab_sizes'],
            embed_dim=config['embed_dim'],
            hidden_dim=config['hidden_dim']
        )
        model.load_state_dict(state['model_state_dict'])
        model.to(machine_device())
        model.eval()

        # Reconstruct preprocessor
        preprocessor = SalesDataPreprocessor()
        preprocessor_state = state['preprocessor_state']

        # Restore label encoders
        for name, le_data in preprocessor_state['label_encoders'].items():
            le = LabelEncoder()
            le.classes_ = np.array(le_data['classes_'])
            preprocessor.label_encoders[name] = le

        # Restore scaler
        preprocessor.scaler.mean_ = np.array(preprocessor_state['scaler_mean'])
        preprocessor.scaler.scale_ = np.array(preprocessor_state['scaler_scale'])
        preprocessor.numeric_features = preprocessor_state['numeric_features']
        preprocessor.categorical_features = preprocessor_state['categorical_features']
        preprocessor.is_fitted = True

        # print(f"Enhanced model loaded from {path}")
        return model, preprocessor

    except Exception as e:
        # print(f"Error loading enhanced model: {str(e)}")
        # raise
        return None, None
    # try


def predict_optimal_price(
    model: EnhancedPricePredictor,
    preprocessor: SalesDataPreprocessor,
    asin: str,
    sku: str,
    marketplace_id: str,
    current_price: float,
    current_quantity: int,
    historical_data: Optional[pd.DataFrame] = None
) -> Dict[str, Any]:
    """
    Predict optimal pricing parameters for a product

    Args:
        model: Trained enhanced price prediction model
        preprocessor: Fitted data preprocessor
        asin: Product ASIN
        sku: Product SKU
        marketplace_id: Marketplace identifier
        current_price: Current product price
        current_quantity: Current quantity sold
        historical_data: Optional historical sales data for temporal features

    Returns:
        Dictionary with pricing recommendations
    """

    model.eval()

    try:
        # Create input data
        input_data = pd.DataFrame({
            'asin': [asin],
            'sku': [sku],
            'order_marketplace_id': [marketplace_id],
            'total_quantity': [current_quantity],
            'total_price': [current_price * current_quantity],
            'prev_total_quantity': [0],  # Will be filled from historical data if available
            'prev_total_price': [0],
            'price_per_quantity': [current_price],
            'prev_price_per_quantity': [current_price],
            'delta_quantity': [0],
            'delta_price_per_quantity': [0]
        })

        # Fill historical information if available
        if historical_data is not None and len(historical_data) > 0:
            # Get the most recent historical data for this product
            product_history = historical_data[
                (historical_data['asin'] == asin) &
                (historical_data['sku'] == sku) &
                (historical_data['order_marketplace_id'] == marketplace_id)
            ].sort_values('order_purchase_date').tail(1)

            if len(product_history) > 0:
                last_record = product_history.iloc[-1]
                input_data['prev_total_quantity'] = [last_record['total_quantity']]
                input_data['prev_total_price'] = [last_record['total_price']]
                input_data['prev_price_per_quantity'] = [last_record['price_per_quantity']]
                input_data['delta_quantity'] = [current_quantity - last_record['total_quantity']]
                input_data['delta_price_per_quantity'] = [
                    current_price - last_record['price_per_quantity']
                ]

        # Transform data
        processed_data = preprocessor.transform(input_data)

        # Move to device
        device = machine_device()
        numeric_features = processed_data['numeric_features'].to(device)
        categorical_features = {
            k: v.to(device) for k, v in processed_data['categorical_features'].items()
        }

        # Create temporal sequence if historical data is available
        temporal_sequence = None
        if historical_data is not None:
            temporal_sequence = preprocessor.create_temporal_sequences(
                historical_data, sequence_length=7
            )
            if temporal_sequence is not None:
                temporal_sequence = temporal_sequence[-1:].to(device)  # Use last sequence

        # Make prediction
        with torch.no_grad():
            predictions = model(numeric_features, categorical_features, temporal_sequence)

        # Convert predictions to interpretable format with better handling
        price_type_logits = predictions['price_type_logits']
        step_point_logits = predictions['step_point_logits']

        # Apply temperature scaling to reduce extreme logits
        temperature = 2.0
        price_type_probs = torch.softmax(price_type_logits / temperature, dim=1)
        step_point_probs = torch.softmax(step_point_logits / temperature, dim=1)

        # Get predictions with confidence
        price_type_idx = torch.argmax(price_type_probs, dim=1).item()
        step_point_idx = torch.argmax(step_point_probs, dim=1).item()

        # Map price type index to string (0=DOWN, 1=UP)
        price_type = "UP" if price_type_idx == 1 else "DOWN"
        step_point = "PERCENT" if step_point_idx == 1 else "VALUE"

        # Ensure step_price is reasonable (minimum 0.1, maximum 50)
        step_price = torch.clamp(predictions['step_price'], min=0.1, max=50.0).item()
        recommended_steps = int(torch.clamp(torch.round(predictions['recommended_steps']), min=1, max=10).item())

        # Calculate confidence scores (probability of chosen class)
        price_type_confidence = price_type_probs[0, price_type_idx].item()
        step_point_confidence = step_point_probs[0, step_point_idx].item()

        # Calculate recommended price
        if step_point == "PERCENT":
            price_change = current_price * (step_price / 100)
        else:
            price_change = step_price

        if price_type == "UP":
            recommended_price = current_price + price_change
        else:  # DOWN
            recommended_price = max(0.01, current_price - price_change)  # Ensure positive price

        return {
            "step_price_type": price_type,
            "step_point": step_point,
            "step_price": round(step_price, 2),
            "recommended_steps": recommended_steps,
            "current_price": current_price,
            "recommended_price": round(recommended_price, 2),
            "price_change": round(price_change, 2),
            "confidence": {
                "price_type": round(price_type_confidence, 3),
                "step_point": round(step_point_confidence, 3)
            },
            "model_version": "enhanced_v1"
        }

    except Exception as e:
        # print(f"Error in price prediction: {str(e)}")
        return {
            "error": str(e),
            "step_price_type": "DOWN",
            "step_point": "VALUE",
            "step_price": 0.01,
            "recommended_steps": 1,
            "current_price": current_price,
            "recommended_price": current_price,
            "price_change": 0.0,
            "confidence": {"price_type": 0.0, "step_point": 0.0},
            "model_version": "enhanced_v1"
        }


def batch_predict_prices(
    model: EnhancedPricePredictor,
    preprocessor: SalesDataPreprocessor,
    products_df: pd.DataFrame,
    historical_data: Optional[pd.DataFrame] = None
) -> List[Dict[str, Any]]:
    """
    Batch prediction for multiple products

    Args:
        model: Trained enhanced price prediction model
        preprocessor: Fitted data preprocessor
        products_df: DataFrame with product information
        historical_data: Optional historical sales data

    Returns:
        List of prediction dictionaries
    """

    results = []

    for _, row in products_df.iterrows():
        prediction = predict_optimal_price(
            model=model,
            preprocessor=preprocessor,
            asin=row['asin'],
            sku=row['sku'],
            marketplace_id=row['order_marketplace_id'],
            current_price=row.get('current_price', row.get('price_per_quantity', 1.0)),
            current_quantity=row.get('current_quantity', row.get('total_quantity', 1)),
            historical_data=historical_data
        )

        prediction['asin'] = row['asin']
        prediction['sku'] = row['sku']
        prediction['marketplace_id'] = row['order_marketplace_id']

        results.append(prediction)

    return results