#!/usr/bin/env python3

from typing import Generator
import pandas as pd
from domains.repository.bas_repository import BasRepository
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository
from datetime import datetime


class Loader:

    def __init__(self, product_repo: ProductRepository, eprice_repo: ElasticPriceRepository, bas_repo: BasRepository):
        self._product_repo = product_repo
        self._eprice_repo = eprice_repo
        self._bas_repo = bas_repo
    #def

    @staticmethod
    def new_instance():
        return Loader(ProductRepository.new_instance(), ElasticPriceRepository.new_instance(), BasRepository.new_instance())
    #def

    def load_to_csv(self,  file_path:str, start_date:datetime, end_date:datetime, limit:int=1000000, offset:int=0):
        data = []
        for new_data in self.load_data(start_date, end_date, limit, offset):
            data.extend(new_data)
        # for

        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
    #def

    def get_customer_ids(self)->list[int]:
        customers =  self._product_repo.get_customer_ids()
        customer_ids = [int(customer['id']) for customer in customers]

        return customer_ids
    #def

    def get_customer_ids_by_strategy(self)->list[int]:
        customers =  self._product_repo.get_customer_ids()

        customer_ids = [int(customer['id']) for customer in customers]

        ep_customer_ids = []
        for customer_id in customer_ids:
            optimizations =  self._product_repo.get_product_optimization_by_strategy(customer_id, ['elasticPricing'], 1, 0)
            if len(optimizations) > 0:
                ep_customer_ids.append(customer_id)
        # for

        return ep_customer_ids
    #def

    def load_data(self, start_date:datetime, end_date:datetime, limit:int=1000000, offset:int=0)->Generator[list[dict], None, None]:
        customers =  self._product_repo.get_customer_ids()

        if len(customers) == 0:
            return
        
        for customer in customers:
            print('='*100)
            print('customer_id=', customer['id'])

            yield from self.load_customer_data(int(customer['id']), start_date, end_date, limit, offset)
        # for
    #def

    def load_customer_data(self, customer_id:int, start_date:datetime, end_date:datetime, limit:int=1000000, offset:int=0)->Generator[list[dict], None, None]:
        continue_loading = True
        load_offset = offset

        while continue_loading:
            history = self._bas_repo.get_all_order_history(customer_id, start_date, end_date, limit, load_offset)
            load_offset = load_offset + limit 

            if len(history) < limit :
                continue_loading = False

            yield history
        # while
    #def    

#class
