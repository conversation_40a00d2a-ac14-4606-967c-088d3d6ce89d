# Elastic Price - Dynamic Price Optimization

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115+-green.svg)](https://fastapi.tiangolo.com/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.7+-red.svg)](https://pytorch.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**Elastic Price** is an advanced machine learning-powered pricing optimization system that uses neural networks to predict optimal pricing strategies for e-commerce products. The system analyzes historical sales data, product characteristics, and market conditions to recommend dynamic pricing adjustments that maximize revenue and sales performance.

## 🚀 Features

- **🧠 Machine Learning Models**: Multiple neural network architectures (EP1, EP2, Enhanced) for price prediction
- **📊 Real-time Predictions**: Fast API endpoints for single and batch price optimization
- **🔄 Continuous Learning**: Automated model retraining with new sales data
- **📈 Multi-dimensional Analysis**: Considers ASIN, SKU, marketplace, historical performance
- **🎯 Business Rules**: Configurable constraints and thresholds for price recommendations
- **🔌 Database Integration**: Support for PostgreSQL, MySQL, ClickHouse, and Redis
- **🐳 Docker Ready**: Containerized deployment with Docker Compose
- **📋 CLI Tools**: Command-line interface for training, testing, and management
- **📚 Comprehensive API**: RESTful API with OpenAPI documentation

## 🏗️ Architecture

The system consists of several key components:

- **API Layer**: FastAPI-based REST API for price predictions
- **ML Models**: PyTorch neural networks for price optimization
- **Data Layer**: Multi-database support for sales history and model storage
- **CLI Tools**: Command-line utilities for model management
- **Background Jobs**: Automated training and data processing

## 📋 Requirements

- **Python**: 3.12 or higher
- **Dependencies**: PyTorch, FastAPI, Pandas, NumPy, Scikit-learn
- **Databases**: PostgreSQL (primary), MySQL, ClickHouse, Redis
- **Hardware**: CUDA-compatible GPU recommended for training

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone https://gitlab.sl.local/sellerlogic/elastic-price.git
cd elastic-price
```

### 2. Set Up Virtual Environment

```bash
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

### 3. Install Dependencies

```bash
# Basic installation
pip install -e .

# Development installation with all tools
pip install -e ".[dev]"

# With Jupyter notebook support
pip install -e ".[jupyter]"

# Install everything
pip install -e ".[all]"
```

### 4. Configure Environment

```bash
# Copy example environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 5. Database Setup

```bash
python3 -m cli.migration migrate-all --directory migrations/elasticpricedb --action=up
```

## ⚙️ Configuration

The application uses environment variables for configuration. Key settings include:

```bash
# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False

# Database Connections
ELASTIC_PRICE_DB_HOST=localhost
ELASTIC_PRICE_DB_PORT=5432
ELASTIC_PRICE_DB_DATABASE=elastic_price
ELASTIC_PRICE_DB_USERNAME=your_username
ELASTIC_PRICE_DB_PASSWORD=your_password

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# ClickHouse Analytics
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_DB_NAME=analytics
```

See `.env.example` for complete configuration options.

## 🚀 Quick Start

### Start the API Server

```bash
# Development server with auto-reload
uvicorn api.app:app --reload --host 0.0.0.0 --port 8000

# Or using the CLI
elastic-price api --host 0.0.0.0 --port 8000
```

### Training Process

```bash
# Full training pipeline
python3 -m cli.ml elastic-price train
```

## 🐳 Docker Deployment

### Using Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f api
```

### Manual Docker Build

```bash
# Build image
docker build -f docker/Dockerfile -t elastic-price .

# Run container
docker run -p 8000:8000 \
  -e API_HOST=0.0.0.0 \
  -e API_PORT=8000 \
  elastic-price
```
