#!/usr/bin/env python3

import logging
from typing import Callable, Dict, Optional, List, Any, Union, Tuple
import pandas as pd
import psycopg

from .pgdb_action import PgAction

# Configure logging
logger = logging.getLogger(__name__)


class Query(PgAction):

    def __init__(self, connection:Optional[psycopg.Connection]):
        super().__init__(connection)
    #def

    def query_all(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> List[Dict[str, Any]]:
        """
        Execute a query on the database.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            Query results as a list of dictionaries
        """
        if not self._connection:
            logger.error("Cannot execute query: No connection to database")
            return []
            
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(query, params or ())
                rows = cursor.fetchall()
                self._connection.commit()
                return rows
        except Exception as e:
            logger.error(f"Query execution error on database: {str(e)}")
            raise
        finally:
            self._connection.commit()
    #def

    def query_one(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> Optional[Dict[str, Any]]:
        """
        Execute a query on the database and return the first result.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            First query result as a dictionary or None if no results
        """
        results = self.query_all(query, params)               

        return results[0] if results else None
    #def
    

    def query_df(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> pd.DataFrame:
        """
        Execute a query on the database and return results as a pandas DataFrame.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            Query results as a pandas DataFrame
        """
        results = self.query_all(query, params)
        return pd.DataFrame(results)
    #def

    def execute(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> int:
        """
        Execute a command on the database that doesn't return results (INSERT, UPDATE, etc.).
        
        Args:
            query: SQL command to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            Number of affected rows
        """
        if not self._connection:
            logger.error("Cannot execute command: No connection to database")
            return 0
            
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(query, params or ())
                affected_rows = cursor.rowcount
                self._connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Command execution error on database: {str(e)}")
            self._connection.rollback()
            raise
    #def

    def batch_execute(self, queries: List[str], params_list: Optional[List[Union[Tuple, Dict, List]]] = None) -> tuple[List[int], Optional[Exception]]:
        """
        Execute multiple commands in sequence on the database.
        
        Args:
            queries: List of SQL commands to execute
            params_list: List of parameters for each query
            
        Returns:
            List of affected row counts
        """
        results = []
        if not self._connection:
            logger.error("Cannot execute batch: No connection to database")
            return results, None
            
        try:
            with self._connection.cursor() as cursor:
                for i, query in enumerate(queries):
                    params = params_list[i] if params_list and i < len(params_list) else None
                    cursor.execute(query, params or ())
                    results.append(cursor.rowcount)
                self._connection.commit()
                return results, None
        except Exception as e:
            logger.error(f"Batch execution error on database: {str(e)}")
            self._connection.rollback()
            return [], e
    #def
#class