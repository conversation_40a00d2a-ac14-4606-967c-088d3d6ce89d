#!/usr/bin/env python3

import logging
from typing import Dict, Optional, List, Any, Union, Tuple
import psycopg

from .pgdb_action import PgAction

# Configure logging
logger = logging.getLogger(__name__)


class Transaction(PgAction):
    """
    Facade for executing transactions against the PostgreSQL databases.
    Handles transaction execution, error handling, and result formatting.
    """

    def __init__(self, connection:Optional[psycopg.Connection]):
        """Initialize the transaction facade."""
        super().__init__(connection)
        self._cursor = None
    #def

    def begin(self) -> None:
        """
        Begin a transaction.
        
        Returns:
            None
        """
        if not self._connection:
            logger.error("Cannot begin transaction: No connection to database")
            return
        
        try:
            # self._connection.set_autocommit(False)
            self._cursor = self._connection.cursor()
        except Exception as e:
            logger.error(f"Transaction begin error on database: {str(e)}")
            raise
    #def

    def commit(self) -> None:
        """
        Commit a transaction.
        
        Returns:
            None
        """
        if self._connection:
            self._connection.commit()
            # self._connection.set_autocommit(True)
        
        if self._cursor:
            self._cursor.close()
            self._cursor = None
    #def

    def rollback(self) -> None:
        """
        Rollback a transaction.
        
        Returns:
            None
        """
        if self._connection:
            self._connection.rollback()
            # self._connection.set_autocommit(True)
        
        if self._cursor:
            self._cursor.close()
            self._cursor = None
    #def

    def execute(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None, raise_exception: bool = True) -> int:
        """
        Execute a command on the database that doesn't return results (INSERT, UPDATE, etc.).
        
        Args:
            query: SQL command to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            Number of affected rows
        """
        if not self._connection or not self._cursor:
            logger.error("Cannot execute command: No connection to database")
            return 0
            
        try:
            self._cursor.execute(query, params or ())
            affected_rows = self._cursor.rowcount
            return affected_rows
        except Exception as e:
            logger.error(f"Command execution error on database: {str(e)}")
            if raise_exception:
                raise
            else:
                return 0
    #def
#class