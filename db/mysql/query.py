#!/usr/bin/env python3

import logging
from typing import Dict, Optional, List, Any, Union, Tuple
import pandas as pd
import pymysql
import time


# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Query:
    """
    Facade for executing queries against the MySQL databases.
    Handles query execution, error handling, and result formatting.
    """

    def __init__(self, connection:Optional[pymysql.Connection]):
        """Initialize the query facade."""
        self._connection = connection
        # logger.info("Query initialized")

    def query_all(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> List[Dict[str, Any]]:
        """
        Execute a query on the database.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Query results as a list of dictionaries
        """
        if not self._connection:
            logger.error("Cannot execute query: No connection to database")
            return []
            
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(query, params or ())
                result = cursor.fetchall()
                self._connection.commit()
                return result
        except Exception as e:
            logger.error(f"Query execution error on database: {str(e)}")
            raise
        finally:
            self._connection.commit()
    #def
    
    def query_one(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> Optional[Dict[str, Any]]:
        """
        Execute a query on the database and return the first result.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            First query result as a dictionary or None if no results
        """
        results = self.query_all(query, params)

        return results[0] if results else None
    #def
    

    def query_df(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> pd.DataFrame:
        """
        Execute a query on the database and return results as a pandas DataFrame.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Query results as a pandas DataFrame
        """
        results = self.query_all(query, params)
        return pd.DataFrame(results)

    
    def execute(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> int:
        """
        Execute a command on the database that doesn't return results (INSERT, UPDATE, etc.).
        
        Args:
            query: SQL command to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Number of affected rows
        """
        if not self._connection:
            logger.error("Cannot execute command: No connection to database")
            return 0
            
        try:
            with self._connection.cursor() as cursor:
                affected_rows = cursor.execute(query, params or ())
                self._connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Command execution error on database: {str(e)}")
            self._connection.rollback()
            raise

    

    def batch_execute(self, queries: List[str]) -> List[int]:
        """
        Execute multiple commands in sequence on a client database.
        
        Args:
            customer_id: Customer ID to connect to
            queries: List of SQL commands to execute
            
        Returns:
            List of affected row counts
        """
        results = []
        if not self._connection:
            logger.error("Cannot execute command: No connection to database")
            return results
            
        try:
            with self._connection.cursor() as cursor:
                for query in queries:
                    affected_rows = cursor.execute(query)
                    results.append(affected_rows)
                self._connection.commit()
                logger.info(f"Batch executed successfully")
        except Exception as e:
            logger.error(f"Batch execution error : {str(e)}")
            self._connection.rollback()
            raise
            
        return results

    def query_with_monitoring(self, query: str, params: Optional[Union[Tuple, Dict]] = None, 
                             timeout: int = 300, progress_callback=None) -> List[Dict[str, Any]]:
        """
        Execute a query with monitoring for long-running operations.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple or dict)
            timeout: Maximum execution time in seconds
            progress_callback: Optional callback function to report progress
            
        Returns:
            Query results as a list of dictionaries
            
        Raises:
            TimeoutError: If query execution exceeds the timeout
        """
        import threading
        import uuid
        
        if not self._connection:
            logger.error("Cannot execute query: No connection to database")
            return []
        
        # Generate a unique connection ID to track this query
        connection_id = None
        query_id = str(uuid.uuid4())[:8]
        
        # Function to check query progress
        def monitor_query():
            nonlocal connection_id
            elapsed = 0
            check_interval = min(5, timeout / 10) if timeout > 0 else 5
            
            try:
                # Get connection ID for the current connection
                with self._connection.cursor() as cursor:
                    cursor.execute("SELECT CONNECTION_ID()")
                    result = cursor.fetchone()
                    connection_id = result.get('CONNECTION_ID()')
                    
                while elapsed < timeout and not monitor_query.cancelled:
                    time.sleep(check_interval)
                    elapsed += check_interval
                    
                    # Report progress if callback provided
                    if progress_callback:
                        progress_callback(query_id, elapsed)
                    
                    # Check if query is still running
                    with self._get_monitoring_connection() as monitor_conn:
                        with monitor_conn.cursor() as cursor:
                            cursor.execute(
                                "SELECT TIME_MS, SQL_TEXT FROM performance_schema.events_statements_current "
                                "WHERE CONNECTION_ID = %s", (connection_id,)
                            )
                            result = cursor.fetchone()
                            if not result:
                                # Query completed
                                monitor_query.cancelled = True
                                return
                
                # If we reached here, the query is still running after timeout
                if not monitor_query.cancelled and connection_id and elapsed >= timeout:
                    logger.warning(f"Query {query_id} exceeded timeout of {timeout}s, killing connection {connection_id}")
                    with self._get_monitoring_connection() as monitor_conn:
                        with monitor_conn.cursor() as cursor:
                            cursor.execute("KILL %s", (connection_id,))
            except Exception as e:
                logger.error(f"Error in query monitor: {str(e)}")
        
        # Initialize monitor state
        monitor_query.cancelled = False
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_query)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            # Execute the query
            logger.info(f"Starting query {query_id}: {query[:100]}...")
            start_time = time.time()
            
            with self._connection.cursor() as cursor:
                cursor.execute(query, params or ())
                result = cursor.fetchall()
            
            # Query completed successfully
            execution_time = time.time() - start_time
            logger.info(f"Query {query_id} completed in {execution_time:.2f}s")
            
            # Cancel the monitor
            monitor_query.cancelled = True
            monitor_thread.join(1.0)  # Wait for monitor to finish
            
            return result
            
        except Exception as e:
            monitor_query.cancelled = True
            monitor_thread.join(1.0)
            
            if isinstance(e, pymysql.err.OperationalError) and "killed" in str(e).lower():
                raise TimeoutError(f"Query execution exceeded timeout of {timeout} seconds")
            
            logger.error(f"Query {query_id} execution error: {str(e)}")
            raise

    def _get_monitoring_connection(self):
        """Get a separate connection for monitoring queries."""
        # This assumes we can get connection details from the existing connection
        if not self._connection:
            raise ValueError("No primary connection available")
            
        # Create a new connection with the same parameters
        config = {
            "host": self._connection.host,
            "port": self._connection.port,
            "user": self._connection.user,
            "password": self._connection.password,
            "database": self._connection.db.decode() if hasattr(self._connection.db, 'decode') else self._connection.db,
            "charset": 'utf8mb4',
            "cursorclass": pymysql.cursors.DictCursor,
            "connect_timeout": 10
        }
        
        return pymysql.connect(**config)
