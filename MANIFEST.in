# Include the README and other documentation files
include README.md
include LICENSE
include CHANGELOG.md
include pyproject.toml
include setup.py

# Include configuration files
include *.yml
include *.yaml
include *.json
include *.toml
include .env.example

# Include package data
recursive-include api *.py
recursive-include cli *.py
recursive-include core *.py
recursive-include domains *.py
recursive-include facades *.py
recursive-include lib *.py
recursive-include repositories *.py
recursive-include utils *.py
recursive-include migrations *.py
recursive-include db *.py

# Include Docker files
include docker/Dockerfile
include docker-compose*.yml

# Include documentation
recursive-include docs *.md
recursive-include docs *.rst
recursive-include docs *.txt

# Exclude test files and directories
recursive-exclude tests *
recursive-exclude test *
recursive-exclude test_* *
recursive-exclude **/test_* *

# Exclude logs and temporary files
recursive-exclude logs *
recursive-exclude __pycache__ *
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
recursive-exclude .pytest_cache *
recursive-exclude .coverage *
recursive-exclude htmlcov *

# Exclude development and build artifacts
recursive-exclude .git *
recursive-exclude .venv *
recursive-exclude venv *
recursive-exclude build *
recursive-exclude dist *
recursive-exclude *.egg-info *
recursive-exclude .tox *
recursive-exclude .mypy_cache *

# Exclude data files that shouldn't be distributed
global-exclude *.csv
global-exclude *.pt
global-exclude *.pkl
global-exclude *.model
recursive-exclude data *
recursive-exclude processed_data* *
recursive-exclude sales_data* *
recursive-exclude torch_model* *
recursive-exclude enhanced_price_model* *

# Exclude IDE and editor files
recursive-exclude .vscode *
recursive-exclude .idea *
global-exclude *.swp
global-exclude *.swo
global-exclude *~
