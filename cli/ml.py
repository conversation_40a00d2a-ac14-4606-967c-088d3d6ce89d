#!/usr/bin/env python3
"""Command-line interface for MyProject."""
from datetime import datetime, timedelta
import sys
import logging
import click

from utils.logging import setup_logging
from config import settings

logger = logging.getLogger(__name__)


@click.group()
@click.pass_context
def cli(ctx):
    ctx.ensure_object(dict)    
    # Setup logging
    setup_logging(log_file_name="cli.log", log_level=settings().env('LOG_LEVEL', 'INFO'))


# Machine learning model commands group
@cli.group()
def elastic_price():
    """Machine learning model commands."""
    pass


# Product classification commands group
@cli.group()
def product_classification():
    """Product classification model commands."""
    pass

# python3 -m cli.ml elastic-price train  2>&1|tee ./logs/console_output.log
@elastic_price.command("train")
def train():
    """learn model for model training."""

    import time
    from facades.redis.redisdb import RedisFacade   
    if RedisFacade.instance().db_query(1).exists("cli.ml__elastic-price__train") :
        print('sleep(60)')
        time.sleep(60)
        return

    RedisFacade.instance().db_query(1).set("cli.ml__elastic-price__train", "1", 60*60)

    # if not is_now_between_midnight_and_one(datetime.now(timezone.utc)):
    #     print("The current time is not between midnight and 1 AM.")
    #     return

    key = "cli.ml__elastic-price__train__ModelEp2Trainer"
    if not RedisFacade.instance().db_query(1).exists(key) :
        print("Training models...")
        try:
            from domains.elasticprice.model_ep2 import ModelEp2Trainer
            trainer = ModelEp2Trainer()
            trainer.train()
        except Exception as e:
            print(f"Error training 2 model: {str(e)}")


        # try:
        #     from domains.elasticprice.enhanced_model_training import main_train
        #     main_train()
        # except Exception as e:
        #     print(f"Error training 3 model: {str(e)}")

        RedisFacade.instance().db_query(1).set(key, "1", 6*60*60)
    # if

    from domains.elasticprice.model_recomendation import main
    main()

    print("Training completed.")
#def


# python3 -m cli.ml elastic-price retrain --model=ep2 --new-model=1 --snapshot-days=3 2>&1|tee ./logs/console_output.log
@elastic_price.command("retrain") 
@click.option("--model", required=True, default="ep2", type=str, help="Model name. Options: ep1, ep2")
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=100, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def retrain(model, new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""


    from domains.elasticprice.enhanced_model_training import main_train
    main_train()

    if model == "ep2":
        from domains.elasticprice.model_ep2 import ModelEp2Trainer
        trainer = ModelEp2Trainer()
        trainer.train(force_new_model=bool(new_model), snapshot_days=snapshot_days, data_bulk_size=data_bulk_size)
    else:
        print("Invalid model name")
#def


# python3 -m cli.ml elastic-price test-train --new-model=1 --snapshot-days=3 2>&1|tee ./logs/test_train_output.log
@elastic_price.command("test-train") 
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=2, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def test_train(new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""

    from domains.elasticprice.model_recomendation import main, generate_recommendations_for_customer
    # main()
    generate_recommendations_for_customer(12640, 10)

    # from domains.repository.eprice_repository import ElasticPriceRepository

    # repo = ElasticPriceRepository.new_instance()
    # customer_id = 19
    # repo.remove_old_recommendations(customer_id, 5)

    # from domains.elasticprice.model_ep2 import ModelEp2Trainer #, load_last_trained_ep3_model, predict, result_from_predicted
    # trainer = ModelEp2Trainer()
    # trainer.train(force_new_model=True, snapshot_days=3)
    # # trainer.train_from_csv('train_data_ep3_20250624134405.csv', force_new_model=True, save_to_file_only=True)

    # from domains.repository.product_repository import ProductRepository
    # repo = ProductRepository.new_instance()
    # items = repo.get_product_optimization_by_strategy(12640, ['elasticPricing'], 1000, 0)
    # print(items)
    # print(len(items))

#def



# python3 -m cli.ml elastic-price product-info --cid=1032 --asin=B09TR96Q7Q --sku=3001-I-Fliegenfransen-Black-cob --marketplace=A1PA6795UKMFR9  2>&1|tee ./logs/test_train_output.log
@elastic_price.command("product-info") 
@click.option("--cid", required=True, default=None, type=int, help="Customer id")
@click.option("--asin", required=True, default=None, type=str, help="ASIN")
@click.option("--sku", required=True, default=None, type=str, help="SKU")
@click.option("--marketplace", required=True, default=None, type=str, help="Marketplace")
def product_info(cid, asin, sku, marketplace):
    """learn model for model training."""
    
    from domains.repository.bas_repository import BasRepository
    from domains.repository.eprice_repository import ElasticPriceRepository
    from pprint import pprint

    bas_repo = BasRepository.new_instance()
    history = bas_repo.get_amazon_product_order_history(cid, asin, sku, marketplace, datetime.now() - timedelta(days=10), datetime.now(), 1000000, 0)

    print('len(history)=', len(history))
    print('history=')
    pprint(history)

    ep_repo = ElasticPriceRepository.new_instance()
    recommendation = ep_repo.get_recommendation(cid, asin, sku, marketplace)
    print('recommendation=')
    pprint(recommendation)
#def


# Product Classification Commands

# python3 -m cli.ml product-classification train --force-new-model=1 --use-real-data=0 --synthetic-limit=2000
@product_classification.command("train")
@click.option("--force-new-model", required=False, default=0, type=int, help="Force new model (1) or continue training (0)")
@click.option("--use-real-data", required=False, default=1, type=int, help="Use real product data (1) or not (0)")
@click.option("--use-synthetic-data", required=False, default=1, type=int, help="Use synthetic data (1) or not (0)")
@click.option("--real-limit", required=False, default=5000, type=int, help="Limit for real data samples")
@click.option("--synthetic-limit", required=False, default=10000, type=int, help="Limit for synthetic data samples")
def train_classification(force_new_model, use_real_data, use_synthetic_data, real_limit, synthetic_limit):
    """Train product classification model."""

    print("Starting product classification training...")

    try:
        from domains.productclassification.classifier_trainer import main_train

        result = main_train(
            force_new_model=bool(force_new_model),
            use_real_data=bool(use_real_data),
            use_synthetic_data=bool(use_synthetic_data),
            real_data_limit=real_limit,
            synthetic_data_limit=synthetic_limit
        )

        if result['success']:
            print(f"✅ Training completed successfully!")
            print(f"Model: {result['model_name']}")
            print(f"Final accuracy: {result['final_accuracy']:.4f}")
            print(f"Training time: {result['training_time']:.1f} seconds")
        else:
            print(f"❌ Training failed: {result['error']}")

    except Exception as e:
        print(f"❌ Training failed with exception: {str(e)}")
        logger.error(f"Product classification training failed: {e}", exc_info=True)


# python3 -m cli.ml product-classification test
@product_classification.command("test")
def test_classification():
    """Test product classification model with sample data."""

    print("Testing product classification model...")

    try:
        from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager

        trainer_manager = ProductClassificationTrainerManager()

        # Test with sample titles
        test_titles = [
            "Apple iPhone 13 Pro Max 128GB Blue",
            "Nike Air Max 270 Running Shoes Black Size 10",
            "Samsung 55-inch 4K Smart TV",
            "Baby Stroller with Car Seat Travel System",
            "Kitchen Knife Set Professional Chef Knives",
            "Wireless Bluetooth Headphones Noise Cancelling",
            "Men's Cotton T-Shirt Blue Large",
            "Laptop Computer Bag Messenger Style",
            "Automotive Car Battery 12V Heavy Duty",
            "Garden Hose 50ft Heavy Duty Waterproof"
        ]

        print("\nTesting predictions...")
        predictions = trainer_manager.predict_products(test_titles)

        print("\n" + "="*80)
        print("PREDICTION RESULTS")
        print("="*80)

        for pred in predictions:
            print(f"\nTitle: {pred['title']}")
            print(f"Category: {pred['predicted_category']}")
            print(f"Confidence: {pred['confidence']:.3f}")
            print(f"Method: {pred['method']}")

            if pred['rule_based_category']:
                print(f"Rule-based: {pred['rule_based_category']}")

            print("Top predictions:")
            for i, top_pred in enumerate(pred['top_predictions'][:3], 1):
                print(f"  {i}. {top_pred['category']} ({top_pred['confidence']:.3f})")

        print("\n" + "="*80)
        print("✅ Testing completed successfully!")

    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        logger.error(f"Product classification testing failed: {e}", exc_info=True)


# python3 -m cli.ml product-classification predict --title="Apple iPhone 13 Pro Max"
@product_classification.command("predict")
@click.option("--title", required=True, type=str, help="Product title to classify")
def predict_single(title):
    """Predict category for a single product title."""

    print(f"Predicting category for: {title}")

    try:
        from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager

        trainer_manager = ProductClassificationTrainerManager()
        result = trainer_manager.trainer.predict_single(title)

        print("\n" + "="*60)
        print("PREDICTION RESULT")
        print("="*60)
        print(f"Title: {result['title']}")
        print(f"Predicted Category: {result['predicted_category']}")
        print(f"Confidence: {result['confidence']:.3f}")
        print(f"Method: {result['method']}")

        if result['rule_based_category']:
            print(f"Rule-based Category: {result['rule_based_category']}")

        print("\nTop 3 Predictions:")
        for i, pred in enumerate(result['top_predictions'], 1):
            print(f"  {i}. {pred['category']} ({pred['confidence']:.3f})")

        print("="*60)

    except Exception as e:
        print(f"❌ Prediction failed: {str(e)}")
        logger.error(f"Product classification prediction failed: {e}", exc_info=True)


# python3 -m cli.ml product-classification model-info
@product_classification.command("model-info")
def model_info():
    """Get information about the current model."""

    print("Getting model information...")

    try:
        from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager

        trainer_manager = ProductClassificationTrainerManager()
        info = trainer_manager.get_model_info()

        print("\n" + "="*60)
        print("MODEL INFORMATION")
        print("="*60)

        if 'error' in info:
            print(f"❌ {info['error']}")
        else:
            print(f"Model Name: {info.get('model_name', 'N/A')}")
            print(f"Model Type: {info.get('model_type', 'N/A')}")

            if 'config' in info:
                config = info['config']
                print(f"Vocabulary Size: {config.get('vocab_size', 'N/A')}")
                print(f"Embedding Dimension: {config.get('embed_dim', 'N/A')}")
                print(f"Hidden Dimension: {config.get('hidden_dim', 'N/A')}")
                print(f"Number of Classes: {config.get('num_classes', 'N/A')}")
                print(f"Learning Rate: {config.get('learning_rate', 'N/A')}")
                print(f"Batch Size: {config.get('batch_size', 'N/A')}")
                print(f"Epochs: {config.get('num_epochs', 'N/A')}")

            if 'categories' in info:
                print(f"Categories ({len(info['categories'])}):")
                for i, cat in enumerate(info['categories'][:10], 1):
                    print(f"  {i}. {cat}")
                if len(info['categories']) > 10:
                    print(f"  ... and {len(info['categories']) - 10} more")

            print(f"Actual Vocabulary Size: {info.get('vocab_size_actual', 'N/A')}")
            print(f"Model Path: {info.get('model_path', 'N/A')}")
            print(f"Vocabulary Path: {info.get('vocab_path', 'N/A')}")

        print("="*60)

    except Exception as e:
        print(f"❌ Failed to get model info: {str(e)}")
        logger.error(f"Failed to get model info: {e}", exc_info=True)


def main():
    """Main entry point for the CLI application."""
    try:
        cli(obj={})
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())


# # If installed as a package
# myproject learn load-data --customer-id=19

# # If running directly from the script
# python -m cli.main learn load-data --customer-id=19
