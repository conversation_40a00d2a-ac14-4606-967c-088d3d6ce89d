#!/usr/bin/env python3

from datetime import datetime, date
from decimal import Decimal
import json
import uuid
from typing import Any, Optional, Union, Dict, List
import logging

# Try to import numpy for type checking
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False

logger = logging.getLogger(__name__)

class JsonEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles various Python types:
    - Decimal: converted to float
    - datetime/date: converted to ISO format strings
    - UUID: converted to string
    - bytes: converted to base64 string
    - sets: converted to lists
    - numpy types: converted to native Python types
    """
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)

        if isinstance(obj, datetime):
            return obj.isoformat()

        if isinstance(obj, date):
            return obj.isoformat()

        if isinstance(obj, uuid.UUID):
            return str(obj)

        if isinstance(obj, bytes):
            import base64
            return base64.b64encode(obj).decode('utf-8')

        if isinstance(obj, set):
            return list(obj)

        # Handle numpy types if numpy is available
        if HAS_NUMPY:
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif hasattr(obj, 'item'):  # numpy scalar
                return obj.item()

        return super(JsonEncoder, self).default(obj)
    
    
class JsonDecoder(json.JSONDecoder):
    """
    Custom JSON decoder that handles:
    - Decimal values
    - ISO format datetime strings
    - UUID strings
    """
    def __init__(self, *args, **kwargs):
        self.parse_decimals = kwargs.pop('parse_decimals', True)
        self.parse_dates = kwargs.pop('parse_dates', True)
        self.parse_uuids = kwargs.pop('parse_uuids', True)
        super().__init__(*args, object_hook=self._object_hook, **kwargs)
        
        if self.parse_decimals:
            self.parse_float = self._parse_float
    
    def _parse_float(self, s):
        """Convert float strings to Decimal objects if enabled."""
        try:
            return Decimal(s)
        except:
            return float(s)
    
    def _object_hook(self, obj):
        """Process objects during JSON decoding."""
        for key, value in obj.items():
            if isinstance(value, str):
                # Try to parse ISO format dates
                if self.parse_dates and len(value) > 10 and 'T' in value:
                    try:
                        obj[key] = datetime.fromisoformat(value)
                    except ValueError:
                        pass
                
                # Try to parse UUID strings
                if self.parse_uuids and len(value) == 36 and '-' in value:
                    try:
                        obj[key] = uuid.UUID(value)
                    except ValueError:
                        pass
        
        return obj


def json_encode(data: Any, cls: Optional[type] = None, **kwargs) -> str:
    """
    Encode Python objects to JSON string with enhanced type support.
    
    Args:
        data: The Python object to encode
        cls: Custom encoder class (defaults to JsonEncoder)
        **kwargs: Additional arguments passed to json.dumps
        
    Returns:
        JSON string representation
    """
    try:
        if cls is None:
            cls = JsonEncoder

        return json.dumps(data, cls=cls, **kwargs)
    except Exception as e:
        logger.error(f"JSON encoding error: {str(e)}")
        raise


def json_decode(data: Union[str, bytes], cls: Optional[type] = None, 
                parse_decimals: bool = True, parse_dates: bool = True, 
                parse_uuids: bool = True, **kwargs) -> Optional[Any]:
    """
    Decode JSON string to Python objects with enhanced type support.
    
    Args:
        data: JSON string or bytes to decode
        cls: Custom decoder class (defaults to JsonDecoder)
        parse_decimals: Whether to parse numbers as Decimal objects
        parse_dates: Whether to parse ISO date strings as datetime objects
        parse_uuids: Whether to parse UUID strings as UUID objects
        **kwargs: Additional arguments passed to json.loads
        
    Returns:
        Decoded Python object or None if decoding fails
    """
    if data is None:
        return None
        
    try:
        if isinstance(data, bytes):
            data = data.decode('utf-8')
            
        if cls is None:
            cls = JsonDecoder
            kwargs.update({
                'parse_decimals': parse_decimals,
                'parse_dates': parse_dates,
                'parse_uuids': parse_uuids
            })
        
        return json.loads(data, cls=cls, **kwargs)
    except Exception as e:
        logger.error(f"JSON decoding error: {str(e)}")
        return None
