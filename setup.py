from setuptools import setup, find_namespace_packages
import os

# Read the contents of README file
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

setup(
    name="elastic_price",
    version="0.2.0",
    packages=find_namespace_packages(exclude=["tests*", "test*", "test_*", "logs*"]),
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml", "*.json"],
    },
    install_requires=[
        # Web framework and API
        "fastapi[standard]>=0.115.12",
        "uvicorn>=0.34.2",
        "gunicorn>=23.0.0",
        "pydantic>=2.11.3",
        "pydantic-settings>=2.9.1",
        "python-dotenv>=1.1.0",
        "python-multipart>=0.0.20",

        # CLI framework
        "click>=8.1.8",
        "typer>=0.15.3",
        "rich>=14.0.0",

        # Data science and ML
        "pandas>=2.2.3",
        "numpy>=2.2.5",
        "scikit-learn>=1.6.1",
        "seaborn>=0.13.2",
        "matplotlib>=3.10.1",
        "scipy>=1.15.2",
        "statsmodels>=0.14.4",

        # PyTorch (CUDA 12.8 support)
        "torch>=2.7.0",
        "torchvision>=0.22.0",
        "torchaudio>=0.22.0",

        # Database connections
        "clickhouse-connect>=0.8.17",
        "PyMySQL>=1.1.1",
        "redis>=5.2.1",
        "psycopg[binary]>=3.2.6",

        # Security and utilities
        "cryptography>=44.0.3",
        "PyYAML>=6.0.2",
        "python-dateutil>=2.9.0",
    ],
    dependency_links=[
        "https://download.pytorch.org/whl/cu128"
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=24.1.0",
            "isort>=5.12.0",
            "mypy>=1.5.1",
            "flake8>=6.1.0",
            "pre-commit>=3.0.0",
        ],
        "jupyter": [
            "jupyterlab>=4.0.0",
            "ipykernel>=6.29.5",
            "ipython>=9.2.0",
            "notebook>=7.0.0",
        ],
        "all": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=24.1.0",
            "isort>=5.12.0",
            "mypy>=1.5.1",
            "flake8>=6.1.0",
            "pre-commit>=3.0.0",
            "jupyterlab>=4.0.0",
            "ipykernel>=6.29.5",
            "ipython>=9.2.0",
            "notebook>=7.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "elastic-price=cli.main:main",
        ],
    },
    python_requires=">=3.12.0",
    author="Vitalii Tykhoniuk",
    author_email="<EMAIL>",
    description="Elastic pricing model for dynamic price optimization using machine learning",
    long_description=long_description,
    long_description_content_type="text/markdown",
    keywords="api, cli, python, pricing, optimization, machine learning, pytorch, fastapi, neural networks",
    url="https://gitlab.sl.local/sellerlogic/elastic-price",
    project_urls={
        "Bug Reports": "https://gitlab.sl.local/sellerlogic/elastic-price/-/issues",
        "Source": "https://gitlab.sl.local/sellerlogic/elastic-price",
        "Documentation": "https://gitlab.sl.local/sellerlogic/elastic-price/-/blob/master/README.md",
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Framework :: FastAPI",
        "Environment :: Console",
        "Environment :: Web Environment",
    ],
    zip_safe=False,
)


# =============================================================================
# DEVELOPMENT AND BUILD INSTRUCTIONS
# =============================================================================

# 1. **Setup development environment**:
#    python3 -m venv .venv
#    source .venv/bin/activate
#    pip install --upgrade pip setuptools wheel

# 2. **Install package in development mode**:
#    pip install -e .                    # Basic installation
#    pip install -e ".[dev]"            # With development dependencies
#    pip install -e ".[jupyter]"        # With Jupyter support
#    pip install -e ".[all]"            # With all optional dependencies

# 3. **Install from requirements.txt** (alternative):
#    pip install -r requirements.txt

# 4. **Build the distribution**:
#    python3 -m build                   # Modern way (requires: pip install build)
#    # OR legacy way:
#    python3 setup.py sdist bdist_wheel

# 5. **Run tests**:
#    pytest tests/                      # Run all tests
#    pytest tests/test_api/             # Run API tests only
#    pytest tests/test_cli/             # Run CLI tests only

# 6. **Code formatting and linting**:
#    black .                           # Format code
#    isort .                           # Sort imports
#    flake8 .                          # Lint code
#    mypy .                            # Type checking

# 7. **Run the application**:
#    # API server
#    uvicorn api.app:app --reload --host 0.0.0.0 --port 8000
#
#    # CLI commands
#    elastic-price --help
#    python3 -m cli.ml elastic-price train
#    python3 -m cli.ml elastic-price retrain --model=ep2 --new-model=0 --snapshot-days=100

# 8. **Docker deployment**:
#    docker-compose up --build         # Build and run with Docker Compose
#    docker build -f docker/Dockerfile -t elastic-price .

# 9. **Publishing** (if needed):
#    pip install twine
#    twine check dist/*                # Validate distribution
#    twine upload dist/*               # Upload to PyPI (requires credentials)
