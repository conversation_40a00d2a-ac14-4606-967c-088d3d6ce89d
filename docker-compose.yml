version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: ./docker/Dockerfile
    container_name: elastic-price-api
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./data:/app/data
    # environment:
    #   - API_HOST=0.0.0.0
    #   - API_PORT=8000
    #   - DATABASE_URL=sqlite:///./data/elastic_price.db
    #   - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-clickhouse}
    #   - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
    #   - CLICKHOUSE_DB_NAME=${CLICKHOUSE_DB_NAME:-default}
    #   - CLICKHOUSE_USERNAME=${CLICKHOUSE_USERNAME:-default}
    #   - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-}
    #   - DATABASE_CLIENT_HOSTS=${DATABASE_CLIENT_HOSTS:-mysql}
    #   - DATABASE_CLIENT_PORT=${DATABASE_CLIENT_PORT:-3306}
    #   - DATABASE_CLIENT_USER=${DATABASE_CLIENT_USER:-root}
    #   - DATABASE_CLIENT_PASSWORD=${DATABASE_CLIENT_PASSWORD:-password}
    #   - REDIS_HOST=${REDIS_HOST:-redis}
    #   - REDIS_PORT=${REDIS_PORT:-6379}
    #   - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    #   - LOG_LEVEL=INFO
    # command: uvicorn api.app:app --host 0.0.0.0 --port 8000 --reload

    # command: uvicorn api.app:app --host 0.0.0.0 --port 8000
    # command: uvicorn app:app --app-dir='api' --host 0.0.0.0 --port 8000 --workers 20 --no-access-log
    # Advanced production setup with Gunicorn
    command: gunicorn api.app:app --workers 20 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --timeout 120 --graceful-timeout 30 --keep-alive 5 --log-level info
    networks:
      - elastic-price-network
    restart: unless-stopped
    # depends_on:
    #   - redis
    #   - mysql

  # cli:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: elastic-price-cli
  #   volumes:
  #     - .:/app
  #     - ./data:/app/data
  #     - ./logs:/app/logs
  #   # environment:
  #   #   - DATABASE_URL=sqlite:///./data/elastic_price.db
  #   #   - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-clickhouse}
  #   #   - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
  #   #   - CLICKHOUSE_DB_NAME=${CLICKHOUSE_DB_NAME:-default}
  #   #   - CLICKHOUSE_USERNAME=${CLICKHOUSE_USERNAME:-default}
  #   #   - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-}
  #   #   - DATABASE_CLIENT_HOSTS=${DATABASE_CLIENT_HOSTS:-mysql}
  #   #   - DATABASE_CLIENT_PORT=${DATABASE_CLIENT_PORT:-3306}
  #   #   - DATABASE_CLIENT_USER=${DATABASE_CLIENT_USER:-root}
  #   #   - DATABASE_CLIENT_PASSWORD=${DATABASE_CLIENT_PASSWORD:-password}
  #   #   - REDIS_HOST=${REDIS_HOST:-redis}
  #   #   - REDIS_PORT=${REDIS_PORT:-6379}
  #   #   - REDIS_PASSWORD=${REDIS_PASSWORD:-}
  #   #   - LOG_LEVEL=INFO
  #   #   - LOG_DIR=/app/logs
  #   # entrypoint: ["python", "-m", "cli.main"]
  #   # command: ["--help"]
  #   networks:
  #     - elastic-price-network
  #   # profiles:
  #     # - cli
  #   # depends_on:
  #   #   - redis
  #   #   - mysql

  # redis:
  #   image: redis:alpine
  #   container_name: elastic-price-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis-data:/data
  #   networks:
  #     - elastic-price-network
  #   restart: unless-stopped
  #   command: redis-server --requirepass ${REDIS_PASSWORD:-}

  # mysql:
  #   image: mysql:8.0
  #   container_name: elastic-price-mysql
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql-data:/var/lib/mysql
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=${DATABASE_CLIENT_PASSWORD:-password}
  #     - MYSQL_DATABASE=repricer_main_db
  #   networks:
  #     - elastic-price-network
  #   restart: unless-stopped

  # clickhouse:
  #   image: clickhouse/clickhouse-server:latest
  #   container_name: elastic-price-clickhouse
  #   ports:
  #     - "8123:8123"
  #     - "9000:9000"
  #   volumes:
  #     - clickhouse-data:/var/lib/clickhouse
  #   networks:
  #     - elastic-price-network
  #   restart: unless-stopped

  # postgres:
  #   image: postgres:16-alpine@sha256:1d74239810c41176e9b3831c68bcc5a2e5b2e8c8b181e1010a56e6da40e03fc5
  #   container_name: elastic-price-postgres
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data
  #   environment:
  #     - POSTGRES_PASSWORD=${REPORT_DB_PASSWORD:-password}
  #     - POSTGRES_USER=${REPORT_DB_USERNAME:-user}
  #     - POSTGRES_DB=${REPORT_DB_DATABASE:-reports}
  #   networks:
  #     - elastic-price-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${REPORT_DB_USERNAME:-user}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

networks:
  elastic-price-network:
    driver: bridge

volumes:
  data:
  logs:
  # redis-data:
  # mysql-data:
  # clickhouse-data:
  # postgres-data:
