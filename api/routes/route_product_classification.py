#!/usr/bin/env python3

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import traceback

from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager
from api.responses.pretty_json_response import PrettyJSONResponse


# Request DTOs
class ClassifyProductRequest(BaseModel):
    title: str = Field(..., description="Product title to classify", min_length=1, max_length=500)


class ClassifyProductBatchRequest(BaseModel):
    titles: List[str] = Field(..., description="List of product titles to classify", min_items=1, max_items=100)


class BatchItemRequest(BaseModel):
    request_id: str = Field(..., description="Unique identifier for this request")
    title: str = Field(..., description="Product title to classify", min_length=1, max_length=500)


class ClassifyProductBatchRequestV2(BaseModel):
    items: List[BatchItemRequest] = Field(..., description="List of products to classify", min_items=1, max_items=100)


# Response DTOs
class CategoryPrediction(BaseModel):
    category: str = Field(..., description="Predicted category name")
    confidence: float = Field(..., description="Confidence score (0-1)")


class ClassifyProductResponse(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message if any")
    title: str = Field(..., description="Original product title")
    predicted_category: str = Field(..., description="Predicted category")
    confidence: float = Field(..., description="Prediction confidence (0-1)")
    rule_based_category: Optional[str] = Field(None, description="Category predicted by rule-based matching")
    method: str = Field(..., description="Prediction method used (neural_network, hybrid)")
    top_predictions: List[CategoryPrediction] = Field(..., description="Top 3 category predictions")
    model_info: Optional[Dict[str, Any]] = Field(None, description="Model information")


class BatchItemResult(BaseModel):
    request_id: str = Field(..., description="Request ID from input")
    success: bool = Field(..., description="Individual prediction success")
    error: Optional[str] = Field(None, description="Error message for this item")
    result: Optional[ClassifyProductResponse] = Field(None, description="Classification result")


class ClassifyProductBatchResponse(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[str] = Field(None, description="Overall error message")
    results: List[ClassifyProductResponse] = Field(..., description="Individual classification results")
    model_info: Optional[Dict[str, Any]] = Field(None, description="Model information")
    total_processed: int = Field(..., description="Total number of items processed")
    successful_predictions: int = Field(..., description="Number of successful predictions")


class ClassifyProductBatchResponseV2(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[str] = Field(None, description="Overall error message")
    results: List[BatchItemResult] = Field(..., description="Individual classification results with request IDs")
    model_info: Optional[Dict[str, Any]] = Field(None, description="Model information")
    total_processed: int = Field(..., description="Total number of items processed")
    successful_predictions: int = Field(..., description="Number of successful predictions")


class ModelInfoResponse(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message if any")
    model_info: Optional[Dict[str, Any]] = Field(None, description="Model information")


# Create router
product_classification_router = APIRouter(tags=["product-classification"])

# Global model manager instance
_model_manager = None


def get_model_manager() -> ProductClassificationTrainerManager:
    """Get or create model manager instance"""
    global _model_manager
    if _model_manager is None:
        _model_manager = ProductClassificationTrainerManager()
        # Try to load existing model
        try:
            _model_manager.trainer.load_model()
        except Exception as e:
            print(f"Warning: Could not load existing model: {e}")
    return _model_manager


@product_classification_router.post(
    "/classify",
    response_class=PrettyJSONResponse,
    response_model=ClassifyProductResponse,
    summary="Classify a single product",
    description="""
    Classify a product into one of the predefined categories based on its title.
    
    The system uses a hybrid approach:
    1. Rule-based matching using category tags
    2. Neural network prediction for complex cases
    
    Returns the predicted category with confidence score and additional metadata.
    """
)
async def classify_product(request: ClassifyProductRequest):
    """Classify a single product title"""
    try:
        model_manager = get_model_manager()
        
        # Get model info
        model_info = model_manager.get_model_info()
        
        # Make prediction
        result = model_manager.trainer.predict_single(request.title)
        
        # Convert to response format
        top_predictions = [
            CategoryPrediction(category=pred['category'], confidence=pred['confidence'])
            for pred in result['top_predictions']
        ]
        
        return ClassifyProductResponse(
            success=True,
            error=None,
            title=result['title'],
            predicted_category=result['predicted_category'],
            confidence=result['confidence'],
            rule_based_category=result['rule_based_category'],
            method=result['method'],
            top_predictions=top_predictions,
            model_info=model_info
        )
        
    except Exception as e:
        error_msg = f"Classification failed: {str(e)}"
        print(f"Error in classify_product: {error_msg}")
        print(traceback.format_exc())
        
        return ClassifyProductResponse(
            success=False,
            error=error_msg,
            title=request.title,
            predicted_category="Unknown",
            confidence=0.0,
            rule_based_category=None,
            method="error",
            top_predictions=[],
            model_info=None
        )


@product_classification_router.post(
    "/classify-batch",
    response_class=PrettyJSONResponse,
    response_model=ClassifyProductBatchResponse,
    summary="Classify multiple products",
    description="""
    Classify multiple products in a single request for better performance.
    
    Accepts up to 100 product titles and returns classification results for each.
    Failed individual predictions are marked with success=false but don't fail the entire request.
    """
)
async def classify_products_batch(request: ClassifyProductBatchRequest):
    """Classify multiple product titles"""
    try:
        model_manager = get_model_manager()
        
        # Get model info
        model_info = model_manager.get_model_info()
        
        # Make predictions
        results = []
        successful_predictions = 0
        
        for title in request.titles:
            try:
                result = model_manager.trainer.predict_single(title)
                
                # Convert to response format
                top_predictions = [
                    CategoryPrediction(category=pred['category'], confidence=pred['confidence'])
                    for pred in result['top_predictions']
                ]
                
                classification_result = ClassifyProductResponse(
                    success=True,
                    error=None,
                    title=result['title'],
                    predicted_category=result['predicted_category'],
                    confidence=result['confidence'],
                    rule_based_category=result['rule_based_category'],
                    method=result['method'],
                    top_predictions=top_predictions,
                    model_info=None  # Don't repeat model info for each item
                )
                
                results.append(classification_result)
                successful_predictions += 1
                
            except Exception as e:
                error_msg = f"Classification failed for '{title}': {str(e)}"
                print(f"Error in batch classification: {error_msg}")
                
                classification_result = ClassifyProductResponse(
                    success=False,
                    error=error_msg,
                    title=title,
                    predicted_category="Unknown",
                    confidence=0.0,
                    rule_based_category=None,
                    method="error",
                    top_predictions=[],
                    model_info=None
                )
                
                results.append(classification_result)
        
        return ClassifyProductBatchResponse(
            success=True,
            error=None,
            results=results,
            model_info=model_info,
            total_processed=len(request.titles),
            successful_predictions=successful_predictions
        )
        
    except Exception as e:
        error_msg = f"Batch classification failed: {str(e)}"
        print(f"Error in classify_products_batch: {error_msg}")
        print(traceback.format_exc())
        
        return ClassifyProductBatchResponse(
            success=False,
            error=error_msg,
            results=[],
            model_info=None,
            total_processed=len(request.titles),
            successful_predictions=0
        )


@product_classification_router.post(
    "/classify-batch-v2",
    response_class=PrettyJSONResponse,
    response_model=ClassifyProductBatchResponseV2,
    summary="Classify multiple products with request IDs",
    description="""
    Classify multiple products with individual request IDs for better tracking.
    
    Each item in the request includes a request_id that is returned in the response,
    making it easier to match requests with responses in async processing scenarios.
    """
)
async def classify_products_batch_v2(request: ClassifyProductBatchRequestV2):
    """Classify multiple product titles with request ID tracking"""
    try:
        model_manager = get_model_manager()
        
        # Get model info
        model_info = model_manager.get_model_info()
        
        # Make predictions
        results = []
        successful_predictions = 0
        
        for item in request.items:
            try:
                result = model_manager.trainer.predict_single(item.title)
                
                # Convert to response format
                top_predictions = [
                    CategoryPrediction(category=pred['category'], confidence=pred['confidence'])
                    for pred in result['top_predictions']
                ]
                
                classification_result = ClassifyProductResponse(
                    success=True,
                    error=None,
                    title=result['title'],
                    predicted_category=result['predicted_category'],
                    confidence=result['confidence'],
                    rule_based_category=result['rule_based_category'],
                    method=result['method'],
                    top_predictions=top_predictions,
                    model_info=None
                )
                
                batch_result = BatchItemResult(
                    request_id=item.request_id,
                    success=True,
                    error=None,
                    result=classification_result
                )
                
                results.append(batch_result)
                successful_predictions += 1
                
            except Exception as e:
                error_msg = f"Classification failed: {str(e)}"
                print(f"Error in batch classification for {item.request_id}: {error_msg}")
                
                batch_result = BatchItemResult(
                    request_id=item.request_id,
                    success=False,
                    error=error_msg,
                    result=None
                )
                
                results.append(batch_result)
        
        return ClassifyProductBatchResponseV2(
            success=True,
            error=None,
            results=results,
            model_info=model_info,
            total_processed=len(request.items),
            successful_predictions=successful_predictions
        )
        
    except Exception as e:
        error_msg = f"Batch classification failed: {str(e)}"
        print(f"Error in classify_products_batch_v2: {error_msg}")
        print(traceback.format_exc())
        
        return ClassifyProductBatchResponseV2(
            success=False,
            error=error_msg,
            results=[],
            model_info=None,
            total_processed=len(request.items),
            successful_predictions=0
        )


@product_classification_router.get(
    "/model-info",
    response_class=PrettyJSONResponse,
    response_model=ModelInfoResponse,
    summary="Get model information",
    description="""
    Get information about the currently loaded product classification model.
    
    Returns model configuration, training metrics, and other metadata.
    """
)
async def get_model_info():
    """Get information about the current model"""
    try:
        model_manager = get_model_manager()
        model_info = model_manager.get_model_info()
        
        return ModelInfoResponse(
            success=True,
            error=None,
            model_info=model_info
        )
        
    except Exception as e:
        error_msg = f"Failed to get model info: {str(e)}"
        print(f"Error in get_model_info: {error_msg}")
        
        return ModelInfoResponse(
            success=False,
            error=error_msg,
            model_info=None
        )
