import logging
from fastapi import APIRouter, Response
from typing import Any, List, Optional
import json
from pydantic import BaseModel, Field

from domains.elasticprice.model import ElasticPriceNN
from domains.elasticprice.model_ep2 import load_model, predict_result
from domains.repository.eprice_repository import ElasticPriceRepository
from facades.redis.redisdb import RedisFacade
from lib.json import json_encode, json_decode


logger = logging.getLogger(__name__)

# Custom response class for formatted JSON
class PrettyJSONResponseV2(Response):
    media_type = "application/json"

    def render(self, content: Any) -> bytes:
        return json.dumps(content, indent=2).encode("utf-8")

# Request DTO
class PredictPriceRequestV2(BaseModel):
    asin: str = Field(..., description="Product asin")
    sku: str = Field(..., description="Product sku")
    marketplace_id: str = Field(..., description="Marketplace identifier")
    
# Parameter DTO for each optimization parameter set
class OptimizationParamsV2(BaseModel):
    step_price_type: str = Field(..., description="Price step type (UP/DOWN)")
    step_price: float = Field(..., description="Price step value")
    step_point: str = Field(..., description="Step point type (VALUE/PERCENT)")
    recommended_steps: int = Field(..., description="Recommended steps")

# Response DTO
class PredictPriceResponseV2(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    optimization: Optional[OptimizationParamsV2] = Field(None, description="Optimization parameters")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Request DTO for batch predictions
class BatchItemRequestV2(BaseModel):
    request_id: str = Field(..., description="Request ID")
    params: PredictPriceRequestV2 = Field(..., description="List of products to predict pricing for")

class PredictPriceBatchRequestV2(BaseModel):
    items: List[BatchItemRequestV2] = Field(..., description="List of products to predict pricing for")

class BatchItemResultV2(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    request_id: str = Field(..., description="Request ID")
    optimization: Optional[OptimizationParamsV2] = Field(None, description="Optimization parameters")


# Response DTO for batch predictions
class PredictPriceBatchResponseV2(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[List[str]] = Field(None, description="Overall error message")
    results: List[BatchItemResultV2] = Field(..., description="Individual prediction results")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Create router for elastic price endpoints
elastic_price2_router = APIRouter(tags=["elastic-price"])
_elastic_price_model_cache = {
    'ai_model': None,
    'model_info': None,
}

def load_last_trained_model(model_name:str = 'ep4') -> tuple[Optional[ElasticPriceNN], dict]:
    cache = RedisFacade.instance().db_query(1)
    global _elastic_price_model_cache

    model_info = json_decode(cache.get('elastic_price_2_last_trained_model'))

    if model_info is not None \
        and _elastic_price_model_cache['model_info'] is not None  \
        and _elastic_price_model_cache['ai_model'] is not None \
        and str(model_info['id']) == str(_elastic_price_model_cache['model_info']['id']):

        ai_model = _elastic_price_model_cache['ai_model']
        model_info = _elastic_price_model_cache['model_info']

        return ai_model, model_info
    # if

    last_model = ElasticPriceRepository.new_instance().get_last_active_trained_model(model_name)
    model_info = {'id' : last_model.id, 'name' : last_model.model_name, 'accuracy' : last_model.accuracy, 'created_at' : last_model.created_at}

    if _elastic_price_model_cache['model_info'] is not None and str(model_info['id']) == str(_elastic_price_model_cache['model_info']['id']):
        ai_model = _elastic_price_model_cache['ai_model']
        model_info = _elastic_price_model_cache['model_info']
    else:
        ai_model = load_model(last_model)
        _elastic_price_model_cache['ai_model'] = ai_model
        _elastic_price_model_cache['model_info'] = model_info
    # if

    cache.set('elastic_price_2_last_trained_model', json_encode(model_info), 60)

    return ai_model, model_info
#def



@elastic_price2_router.post(
    "/predict", 
    response_class=PrettyJSONResponseV2, 
    response_model=PredictPriceResponseV2,
    summary="Predict elastic pricing parameters",
    description="""
    Predicts optimal pricing parameters based on product information.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal pricing strategies.
    """,
    responses={
        200: {
            "description": "Successful prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "optimization": {
                            "step_price_type": "UP",
                            "step_price": 5.0,
                            "step_point": "VALUE",
                            "recommended_steps": 1,
                        },
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to predict price",
                        "model_info": {},
                        "optimization": None
                    }
                }
            }
        }
    }
)
async def predict_price(request: PredictPriceRequestV2):
    """
    Predict optimal pricing parameters for a product.
    
    The endpoint accepts product details and returns a list of pricing optimization 
    parameters that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    ai_model, model_info = load_last_trained_model()

    if not ai_model:
        return PredictPriceResponseV2(success=False, error="Failed to load model", model_info=None, optimization=None)

    # Predict the result
    optimization = predict_result(ai_model, request.asin, request.sku, request.marketplace_id)

    if optimization is None:
        return PredictPriceResponseV2(success=False, error="Failed to predict price", model_info=model_info, optimization=None)
         


    # Return the PredictPriceResponse object instead of the raw result
    return PredictPriceResponseV2(
        success=True, error="", model_info=model_info, 
        optimization=optimization,
    )
#def

@elastic_price2_router.post(
    "/predict-batch", 
    response_class=PrettyJSONResponseV2, 
    response_model=PredictPriceBatchResponseV2,
    summary="Predict elastic pricing parameters for multiple products",
    description="""
    Predicts optimal pricing parameters for multiple products in a single request.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal 
    pricing strategies for each product in the batch.
    """,
    responses={
        200: {
            "description": "Successful batch prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "results": [
                            {
                                "request_id": "1", 
                                "optimization": {
                                    "step_price_type": "UP",
                                    "step_price": 5.0,
                                    "step_point": "VALUE",
                                    "recommended_steps": 1,
                                }
                            },
                            {
                                "request_id": "2",
                                "optimization": {
                                    "step_price_type": "DOWN",
                                    "step_price": 10.0,
                                    "step_point": "PERCENT",
                                    "recommended_steps": 1,
                                }
                            }
                        ]
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "items", 0, "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to process batch prediction",
                        "model_info": None,
                        "results": []
                    }
                }
            }
        }
    }
)
async def predict_price_batch(request: PredictPriceBatchRequestV2):
    """
    Predict optimal pricing parameters for multiple products in a batch.
    
    The endpoint accepts a list of product details and returns a list of pricing optimization 
    parameters for each product that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    # Load model once for all predictions
    ai_model, model_info = load_last_trained_model()
    
    if not ai_model:
        return PredictPriceBatchResponseV2(
            success=False, 
            error="Failed to load model", 
            model_info=None, 
            results=[]
        )
    
    # Process each request in the batch
    results = []
    errors = []
    for item in request.items:
        try:
            optimization = predict_result(
                ai_model,
                item.params.asin, 
                item.params.sku, 
                item.params.marketplace_id,
            )
            
            
            if optimization is None:
                results.append(
                    BatchItemResultV2(
                        success=False,
                        error="Failed to predict price",
                        request_id=item.request_id,
                        optimization=None
                    )
                )
                continue
            
            results.append(
                BatchItemResultV2(
                    success=True, 
                    error=None,
                    request_id=item.request_id,
                    optimization=optimization
                )
            )
        except Exception as e:
            logger.error(f"Error processing batch item: {str(e)}")
            results.append(
                BatchItemResultV2(
                    success=False, 
                    error=f"Error processing request: {str(e)}",
                    request_id=item.request_id,
                    optimization=None
                )
            )
            errors.append(f"Error processing request: {str(e)}")
    

    # Return the batch response
    return PredictPriceBatchResponseV2(
        success=True if not errors else False,
        error=errors,
        model_info=model_info,
        results=results
    )
#def

