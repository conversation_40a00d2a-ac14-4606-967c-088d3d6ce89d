import logging
from fastapi import APIRouter, Response
from typing import Any, List, Optional
import json
from pydantic import BaseModel, Field

from domains.elasticprice.enhanced_price_model import EnhancedPricePredictor, SalesDataPreprocessor, load_enhanced_model, predict_optimal_price
from domains.repository.eprice_repository import ElasticPriceRepository
from facades.redis.redisdb import RedisFacade
from lib.json import json_encode, json_decode


logger = logging.getLogger(__name__)

# Custom response class for formatted JSON
class PrettyJSONResponseV3(Response):
    media_type = "application/json"

    def render(self, content: Any) -> bytes:
        return json.dumps(content, indent=2).encode("utf-8")

# Request DTO
class PredictPriceRequestV3(BaseModel):
    asin: str = Field(..., description="Product asin")
    sku: str = Field(..., description="Product sku")
    marketplace_id: str = Field(..., description="Marketplace identifier")
    customer_id: int = Field(..., description="Customer identifier")
    current_price: float = Field(..., description="Current price")
    current_quantity: int = Field(..., description="Current quantity")
    
# Parameter DTO for each optimization parameter set
class OptimizationParamsV3(BaseModel):
    step_price_type: str = Field(..., description="Price step type (UP/DOWN)")
    step_price: float = Field(..., description="Price step value")
    step_point: str = Field(..., description="Step point type (VALUE/PERCENT)")
    recommended_steps: int = Field(..., description="Recommended steps")

# Response DTO
class PredictPriceResponseV3(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    optimization: Optional[OptimizationParamsV3] = Field(None, description="Optimization parameters")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Request DTO for batch predictions
class BatchItemRequestV3(BaseModel):
    request_id: str = Field(..., description="Request ID")
    params: PredictPriceRequestV3 = Field(..., description="List of products to predict pricing for")

class PredictPriceBatchRequestV3(BaseModel):
    items: List[BatchItemRequestV3] = Field(..., description="List of products to predict pricing for")

class BatchItemResultV3(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    request_id: str = Field(..., description="Request ID")
    optimization: Optional[OptimizationParamsV3] = Field(None, description="Optimization parameters")


# Response DTO for batch predictions
class PredictPriceBatchResponseV3(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[List[str]] = Field(None, description="Overall error message")
    results: List[BatchItemResultV3] = Field(..., description="Individual prediction results")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Create router for elastic price endpoints
elastic_price3_router = APIRouter(tags=["elastic-price"])
_elastic_price_model_cache = {
    'ai_model': None,
    'preprocessor': None,
    'last_model_dict': None,
}

def load_last_trained_model(model_name:str = 'ep5') -> tuple[Optional[EnhancedPricePredictor], Optional[SalesDataPreprocessor], Optional[dict]]:
    cache = RedisFacade.instance().db_query(1)
    global _elastic_price_model_cache

    last_model_dict = json_decode(cache.get('elastic_price_3_last_trained_model'))

    if last_model_dict is not None \
        and _elastic_price_model_cache['last_model_dict'] is not None  \
        and _elastic_price_model_cache['ai_model'] is not None \
        and str(last_model_dict['id']) == str(_elastic_price_model_cache['last_model_dict']['id']):

        ai_model = _elastic_price_model_cache['ai_model']
        last_model_dict = _elastic_price_model_cache['last_model_dict']
        preprocessor = _elastic_price_model_cache['preprocessor']

        return ai_model, preprocessor, last_model_dict
    # if

    last_model = ElasticPriceRepository.new_instance().get_last_active_trained_model(model_name)
    last_model_dict = {'id' : last_model.id, 'name' : last_model.model_name, 'created_at' : last_model.created_at, 'model_info': last_model.model_info}

    if _elastic_price_model_cache['ai_model'] is not None and _elastic_price_model_cache['last_model_dict'] is not None and str(last_model_dict['id']) == str(_elastic_price_model_cache['last_model_dict']['id']):
        ai_model = _elastic_price_model_cache['ai_model']
        last_model_dict = _elastic_price_model_cache['last_model_dict']
        preprocessor = _elastic_price_model_cache['preprocessor']
    else:
        ai_model, preprocessor = load_enhanced_model(path=None, model_dump=last_model.model_dump)
        _elastic_price_model_cache['ai_model'] = ai_model
        _elastic_price_model_cache['preprocessor'] = preprocessor
        _elastic_price_model_cache['last_model_dict'] = last_model_dict
    # if

    cache.set('elastic_price_3_last_trained_model', json_encode(last_model_dict), 30*60)

    return ai_model, preprocessor, last_model_dict
#def



@elastic_price3_router.post(
    "/predict", 
    response_class=PrettyJSONResponseV3, 
    response_model=PredictPriceResponseV3,
    summary="Predict elastic pricing parameters",
    description="""
    Predicts optimal pricing parameters based on product information.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal pricing strategies.
    """,
    responses={
        200: {
            "description": "Successful prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "optimization": {
                            "step_price_type": "UP",
                            "step_price": 5.0,
                            "step_point": "VALUE",
                            "recommended_steps": 1,
                        },
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to predict price",
                        "model_info": {},
                        "optimization": None
                    }
                }
            }
        }
    }
)
async def predict_price(request: PredictPriceRequestV3):
    """
    Predict optimal pricing parameters for a product.
    
    The endpoint accepts product details and returns a list of pricing optimization 
    parameters that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    ai_model, preprocessor, last_model_dict = load_last_trained_model()

    if not ai_model or not preprocessor:
        return PredictPriceResponseV3(success=False, error="Failed to load model", model_info=None, optimization=None)


    prediction = predict_optimal_price(
        model=ai_model,
        preprocessor=preprocessor,
        asin=request.asin,
        sku=request.sku,
        marketplace_id=request.marketplace_id,
        current_price=request.current_price,
        current_quantity=request.current_quantity
    )

    if prediction is None:
        return PredictPriceResponseV3(success=False, error="Failed to predict price", model_info=last_model_dict, optimization=None)
         

    # Return the PredictPriceResponse object instead of the raw result
    return PredictPriceResponseV3(
        success=True, error="", model_info=last_model_dict, 
        optimization= OptimizationParamsV3(
            step_price_type=prediction['step_price_type'],
            step_price=prediction['step_price'],
            step_point=prediction['step_point'],
            recommended_steps=prediction['recommended_steps']
        ),
    )
#def

@elastic_price3_router.post(
    "/predict-batch", 
    response_class=PrettyJSONResponseV3, 
    response_model=PredictPriceBatchResponseV3,
    summary="Predict elastic pricing parameters for multiple products",
    description="""
    Predicts optimal pricing parameters for multiple products in a single request.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal 
    pricing strategies for each product in the batch.
    """,
    responses={
        200: {
            "description": "Successful batch prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "results": [
                            {
                                "request_id": "1", 
                                "optimization": {
                                    "step_price_type": "UP",
                                    "step_price": 5.0,
                                    "step_point": "VALUE",
                                    "recommended_steps": 1,
                                }
                            },
                            {
                                "request_id": "2",
                                "optimization": {
                                    "step_price_type": "DOWN",
                                    "step_price": 10.0,
                                    "step_point": "PERCENT",
                                    "recommended_steps": 1,
                                }
                            }
                        ]
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "items", 0, "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to process batch prediction",
                        "model_info": None,
                        "results": []
                    }
                }
            }
        }
    }
)
async def predict_price_batch(request: PredictPriceBatchRequestV3):
    """
    Predict optimal pricing parameters for multiple products in a batch.
    
    The endpoint accepts a list of product details and returns a list of pricing optimization 
    parameters for each product that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    # Load model once for all predictions
    ai_model, preprocessor, last_model_dict = load_last_trained_model()
    
    if not ai_model or not preprocessor:
        return PredictPriceBatchResponseV3(
            success=False, 
            error="Failed to load model", 
            model_info=None, 
            results=[]
        )
    
    # Process each request in the batch
    results = []
    errors = []
    for item in request.items:
        try:

            prediction = predict_optimal_price(
                model=ai_model,
                preprocessor=preprocessor,
                asin=item.params.asin,
                sku=item.params.sku,
                marketplace_id=item.params.marketplace_id,
                current_price=item.params.current_price,
                current_quantity=item.params.current_quantity
            )


            if prediction is None:
                results.append(
                    BatchItemResultV3(
                        success=False,
                        error="Failed to predict price",
                        request_id=item.request_id,
                        optimization=None
                    )
                )
                continue
            
            results.append(
                BatchItemResultV3(
                    success=True,
                    error=None,
                    request_id=item.request_id,
                    optimization=OptimizationParamsV3(
                        step_price_type=prediction['step_price_type'],
                        step_price=prediction['step_price'],
                        step_point=prediction['step_point'],
                        recommended_steps=prediction['recommended_steps']
                    )
                )
            )
        except Exception as e:
            logger.error(f"Error processing batch item: {str(e)}")
            results.append(
                BatchItemResultV3(
                    success=False,
                    error=f"Error processing request: {str(e)}",
                    request_id=item.request_id,
                    optimization=None
                )
            )
            errors.append(f"Error processing request: {str(e)}")
    

    # Return the batch response
    return PredictPriceBatchResponseV3(
        success=True if not errors else False,
        error=errors,
        model_info=last_model_dict,
        results=results
    )
#def