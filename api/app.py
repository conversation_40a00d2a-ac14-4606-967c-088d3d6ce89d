
from datetime import datetime
import sys
import os
import logging
# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

"""Main API application module."""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from api.routes.route_ep2 import elastic_price2_router as elastic_price_2_router
from api.routes.route_recommendation import elastic_price_recommendation_router
from api.routes.route_product_classification import product_classification_router
# from api.routes.route_ep3 import elastic_price3_router as elastic_price_3_router
from api.middleware.logging import LoggingMiddleware
from utils.logging import setup_logging
from config import settings


# Setup logging
setup_logging(settings().env('LOG_LEVEL', 'INFO'), 'api.log' )
logger = logging.getLogger(__name__)

# !!!!!!!! SERVER RUN !!!!!!!!!!!!
# uvicorn app:app --host='0.0.0.0' --port 8000  --reload --app-dir='api'
# uvicorn app:app --app-dir='api' --host 0.0.0.0 --port 8000 --workers 4 --no-access-log

app = FastAPI(
    title="ElasticPrice API",
    description="API for ElasticPrice application",
    version="0.2.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings().CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom logging middleware
app.add_middleware(LoggingMiddleware)

# Include routers
app.include_router(elastic_price_2_router, prefix="/api/v2/elastic-price")
app.include_router(elastic_price_recommendation_router, prefix="/api/v3/elastic-price")
app.include_router(product_classification_router, prefix="/api/v1/product-classification")
# app.include_router(elastic_price_3_router, prefix="/api/v3/elastic-price")

@app.get("/", 
    summary="API root information",
    description="Returns basic information about the API including version and status",
    response_description="Basic API information",
    responses={
        200: {
            "description": "Successful response with API information",
            "content": {
                "application/json": {
                    "example": {
                        "name": "Elastic Price API",
                        "version": "0.2.0",
                        "status": "running",
                        "timestamp": "2023-06-15 10:30:00",
                        "envs": {
                            "DEBUG": False
                        }
                    }
                }
            }
        }
    }
)
async def root():
    logger.info("Root endpoint accessed")
    return {
        "name": "Elastic Price API",
        "version": "0.2.0",
        "status": "running",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "envs": {
            "DEBUG": settings().DEBUG,
        }
    }

@app.get("/health",
    summary="Health check for system status",
    description="Performs connectivity checks to all dependent services and databases",
    response_description="Status of all service connections",
    responses={
        200: {
            "description": "Successful health check with service statuses",
            "content": {
                "application/json": {
                    "example": {
                        "checks": {
                            "mainDb": "OK",
                            "clientDb": "OK",
                            "elasticPriceDb": "OK",
                            "redisDb": "OK",
                            "basDb": "OK"
                        },
                        "date": "2023-06-15 10:30:00"
                    }
                }
            }
        },
        500: {
            "description": "Health check with failed connections",
            "content": {
                "application/json": {
                    "example": {
                        "checks": {
                            "mainDb": "OK",
                            "clientDb": "Connection error",
                            "elasticPriceDb": "OK",
                            "redisDb": "Connection error",
                            "basDb": "OK"
                        },
                        "date": "2023-06-15 10:30:00"
                    }
                }
            }
        }
    }
)
async def health_check():
    logger.debug("Health check endpoint accessed")

    from facades.repricer.repricerdb import RepricerDbFacade
    from facades.redis.redisdb import RedisFacade
    from facades.elasticpricedb.db import ElasticPriceDbFacade
    from facades.baservice.basdb import BasDbFacade

    return {
        "checks" : {
            "mainDb" : "OK" if (RepricerDbFacade.instance().main_db_query().query_one("SELECT 1") is not None)  else "Connection error" ,
            "clientDb" : "OK" if (RepricerDbFacade.instance().client_db_query(1).query_one("SELECT 1") is not None) else "Connection error",
            "elasticPriceDb" : "OK" if (ElasticPriceDbFacade.instance().db_query().query_one("SELECT 1") is not None) else "Connection error",
            "redisDb" : "OK" if (RedisFacade.instance().db(1) and RedisFacade.instance().db(1).ping() is not None) else "Connection error",
            "basDb" : "OK" if (BasDbFacade.instance().client_db_query(1).query_one("SELECT 1") is not None) else "Connection error",
        },
        "date" : datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }


# Add exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for all unhandled exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return {
        "error": "Internal Server Error",
        "detail": str(exc) if settings().DEBUG else "An unexpected error occurred",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

